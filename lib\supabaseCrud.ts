// --- Begin lib/supabaseCrud.ts ---
import { supabase, supabaseAdmin } from "./supabase";
import { Database } from "./database.types"; // Use generated types
import { PostgrestError } from "@supabase/supabase-js";
import { cache, CACHE_KEYS } from "./cacheService";
import { networkService } from "./networkService";
import { offlineQueue } from "./offlineQueueService";
import { achievements } from "./achievementsService";
import { streaks } from "./streaksService";
import { challenges } from "./challengesService";
import { collections } from "./collectionsService";

// Define aliases for generated types for convenience
type DbTables = Database["public"]["Tables"];
type DbEnums = Database["public"]["Enums"];

type Profile = DbTables["profiles"]["Row"];
type ProfileInsert = DbTables["profiles"]["Insert"];
type ProfileUpdate = DbTables["profiles"]["Update"];

type Event = DbTables["events"]["Row"];
type EventInsert = DbTables["events"]["Insert"];
type EventUpdate = DbTables["events"]["Update"];

type Participant = DbTables["participants"]["Row"];
export type ParticipantInsert = DbTables["participants"]["Insert"];
type ParticipantUpdate = DbTables["participants"]["Update"];

type Item = DbTables["items"]["Row"];
export type ItemInsert = DbTables["items"]["Insert"];
type ItemUpdate = DbTables["items"]["Update"];

type Contact = DbTables["contacts"]["Row"];
export type ContactInsert = DbTables["contacts"]["Insert"];
type ContactUpdate = DbTables["contacts"]["Update"];

type ContactGroup = DbTables["contact_groups"]["Row"];
type ContactGroupInsert = DbTables["contact_groups"]["Insert"];
type ContactGroupUpdate = DbTables["contact_groups"]["Update"];

type ContactGroupMember = DbTables["contact_group_members"]["Row"];
type ContactGroupMemberInsert = DbTables["contact_group_members"]["Insert"];

// Enums from generated types
type ParticipantRoleEnum = DbEnums["participant_role"];
type ParticipantStatusEnum = DbEnums["participant_status"];

// Type returned by the get_events_for_user RPC function
type EventFromRpc =
  Database["public"]["Functions"]["get_events_for_user"]["Returns"][number];
// Custom combined type for Event list display
export interface EventWithParticipantStatus extends EventFromRpc {
  is_organizer: boolean;
  participant_status?: ParticipantStatusEnum | null;
  participant_role?: ParticipantRoleEnum | null;
}

// Helper function to handle Supabase errors
function handleError(error: PostgrestError | null, context: string): void {
  if (error) {
    console.error(`Supabase error in ${context}:`, error.message);
    console.error(`Error details:`, error);

    // Log specific error types for better debugging
    if (error.message.includes("infinite recursion")) {
      console.warn(
        `RLS recursion detected in ${context} - consider using admin client`
      );
    }
    if (error.message.includes("does not exist")) {
      console.warn(`Missing database object in ${context} - check schema`);
    }
    if (error.message.includes("permission denied")) {
      console.warn(`Permission denied in ${context} - check RLS policies`);
    }
  }
}

// --- Profiles ---

export async function fetchProfile(userId: string): Promise<Profile | null> {
  try {
    // Utiliser le client standard avec gestion d'erreurs robuste
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (error) {
      console.warn("fetchProfile: Error occurred:", error.message);
      // Si le profil n'existe pas, essayer de le créer
      if (error.code === "PGRST116" || error.message.includes("0 rows")) {
        return await createProfile(userId, {
          name: "Utilisateur",
          avatar_url: null,
        });
      }
      handleError(error, "fetchProfile");
      return null;
    }

    // Si aucun profil n'existe, en créer un automatiquement
    if (!data) {
      return await createProfile(userId, {
        name: "Utilisateur",
        avatar_url: null,
      });
    }

    return data;
  } catch (e) {
    console.error("Exception in fetchProfile:", e);
    // En dernier recours : créer un profil manuellement
    try {
      return await createProfile(userId, {
        name: "Utilisateur",
        avatar_url: null,
      });
    } catch (createError) {
      console.error("Failed to create profile as last resort:", createError);
      return null;
    }
  }
}

export async function createProfile(
  userId: string,
  profileData: Omit<ProfileInsert, "id">
): Promise<Profile | null> {
  try {
    // Utiliser le client standard pour créer le profil
    const { data, error } = await supabase
      .from("profiles")
      .insert({
        id: userId,
        ...profileData,
      })
      .select()
      .single();

    if (error) {
      handleError(error, "createProfile");
      return null;
    }

    return data;
  } catch (e) {
    console.error("Exception in createProfile:", e);
    return null;
  }
}

export async function updateProfile(
  userId: string,
  updates: ProfileUpdate
): Promise<Profile | null> {
  const { id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("profiles")
    .update(updatePayload)
    .eq("id", userId)
    .select()
    .single();
  handleError(error, "updateProfile");
  return data;
}

export async function searchProfiles(query: string): Promise<Profile[]> {
  const { data, error } = await supabase
    .from("profiles")
    .select("*")
    .ilike("name", `%${query}%`)
    .limit(10);
  handleError(error, "searchProfiles");
  return data || [];
}

// --- Events ---

export async function fetchEventsForUser(
  userId: string
): Promise<EventWithParticipantStatus[]> {
  try {
    // Vérifier d'abord le cache
    const cacheKey = CACHE_KEYS.EVENTS;
    const cachedEvents = await cache.get<EventWithParticipantStatus[]>(
      cacheKey,
      { userId }
    );

    if (cachedEvents && networkService.isConnected()) {
      console.log("Events loaded from cache");
      return cachedEvents;
    }

    // Si pas de réseau, utiliser le cache uniquement
    if (!networkService.isConnected()) {
      console.log("Offline mode: using cached events only");
      return cachedEvents || [];
    }

    // Try RPC function first
    const { data, error } = await supabase.rpc("get_events_for_user", {
      user_id_param: userId,
    });

    // If RPC function doesn't exist or fails, use fallback
    if (
      error?.message.includes("does not exist") ||
      error?.message.includes("function")
    ) {
      console.warn(
        "RPC function get_events_for_user not found, using fallback"
      );
      const fallbackResult = await fetchEventsForUserFallback(userId);

      // Mettre en cache le résultat
      if (fallbackResult.length > 0) {
        await cache.set(cacheKey, fallbackResult, { userId });
      }

      return fallbackResult;
    }

    if (error) {
      handleError(error, "fetchEventsForUser (RPC)");
      const fallbackResult = await fetchEventsForUserFallback(userId);

      // Mettre en cache le résultat
      if (fallbackResult.length > 0) {
        await cache.set(cacheKey, fallbackResult, { userId });
      }

      return fallbackResult;
    }

    if (!data) return cachedEvents || [];

    // Map the RPC result, assuming it returns fields compatible with EventFromRpc
    const events = data.map((event) => ({
      ...event,
      is_organizer: event.organizer_id === userId,
      participant_status: null, // Placeholder - RPC doesn't return this
      participant_role: null, // Placeholder - RPC doesn't return this
    }));

    // Mettre en cache le résultat
    if (events.length > 0) {
      await cache.set(cacheKey, events, { userId });
    }

    return events;
  } catch (e) {
    console.error("Exception in fetchEventsForUser:", e);

    // En cas d'erreur, essayer le cache puis le fallback
    const cachedEvents = await cache.get<EventWithParticipantStatus[]>(
      cacheKey,
      { userId }
    );
    if (cachedEvents) {
      return cachedEvents;
    }

    return await fetchEventsForUserFallback(userId);
  }
}

// Fallback function using direct queries
async function fetchEventsForUserFallback(
  userId: string
): Promise<EventWithParticipantStatus[]> {
  try {
    // Récupérer les événements où l'utilisateur est organisateur
    const { data: organizedEvents, error: organizedError } = await supabase
      .from("events")
      .select("*")
      .eq("organizer_id", userId);

    if (organizedError) {
      handleError(
        organizedError,
        "fetchEventsForUserFallback - organized events"
      );
    }

    // Récupérer les événements où l'utilisateur est participant
    const { data: participantEvents, error: participantError } = await supabase
      .from("participants")
      .select("event_id, events(*)")
      .eq("user_id", userId);

    if (participantError) {
      handleError(
        participantError,
        "fetchEventsForUserFallback - participant events"
      );
    }

    // Combiner et dédupliquer les événements
    const allEvents: Event[] = [];
    const eventIds = new Set<number>();

    // Ajouter les événements organisés
    if (organizedEvents) {
      organizedEvents.forEach((event) => {
        if (!eventIds.has(event.id)) {
          allEvents.push(event);
          eventIds.add(event.id);
        }
      });
    }

    // Ajouter les événements de participation
    if (participantEvents) {
      participantEvents.forEach((p) => {
        if (p.events && !eventIds.has(p.events.id)) {
          allEvents.push(p.events);
          eventIds.add(p.events.id);
        }
      });
    }

    // Mapper vers EventWithParticipantStatus
    return allEvents.map((event) => ({
      ...event,
      is_organizer: event.organizer_id === userId,
      participant_status: null,
      participant_role: null,
    }));
  } catch (e) {
    console.error("Exception in fetchEventsForUserFallback:", e);
    return [];
  }
}

export async function fetchEventDetails(
  eventId: number
): Promise<Event | null> {
  try {
    // Utiliser uniquement le client standard
    const { data, error } = await supabase
      .from("events")
      .select("*")
      .eq("id", eventId)
      .single();

    if (error) {
      handleError(error, "fetchEventDetails");
      return null;
    }

    return data;
  } catch (e) {
    console.error("Exception in fetchEventDetails:", e);
    return null;
  }
}

export async function createEvent(
  eventData: Omit<
    EventInsert,
    "id" | "created_at" | "updated_at" | "organizer_id"
  >,
  organizerId: string
): Promise<Event | null> {
  if (!eventData.title || !eventData.date_time) {
    return null;
  }
  const eventDate = new Date(eventData.date_time);
  if (eventDate <= new Date()) {
    throw new Error("La date de l'événement doit être dans le futur");
  }
  try {
    const eventPayload: EventInsert = {
      ...eventData,
      organizer_id: organizerId,
    };

    // Si pas de réseau, ajouter à la queue hors-ligne
    if (!networkService.isConnected()) {
      console.log("Offline mode: adding event creation to queue");

      // Générer un ID temporaire pour l'événement
      const tempId = Date.now();
      const tempEvent: Event = {
        ...eventPayload,
        id: tempId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Ajouter à la queue
      await offlineQueue.add("CREATE", "event", eventPayload, {
        priority: "high",
        userId: organizerId,
      });

      // Invalider le cache des événements
      await cache.invalidate(CACHE_KEYS.EVENTS);

      return tempEvent;
    }

    // Utiliser le client standard uniquement
    const { data, error } = await supabase
      .from("events")
      .insert(eventPayload)
      .select()
      .single();

    if (error) {
      handleError(error, "createEvent");
      return null;
    }

    // Invalider le cache des événements
    await cache.invalidate(CACHE_KEYS.EVENTS);

    // Déclencher les achievements et streaks pour la création d'événement
    try {
      await achievements.updateStats(organizerId, { events_organized: 1 });
      await achievements.checkAchievements(organizerId);

      // Mettre à jour les streaks
      await streaks.updateWeeklyEvent(organizerId);

      // Mettre à jour la progression des challenges
      await challenges.updateProgress(organizerId, "events_created", 1);

      // Vérifier les collections complétées
      await collections.checkCompletion(organizerId);
    } catch (achievementError) {
      console.warn(
        "Error updating achievements for event creation:",
        achievementError
      );
    }

    return data;
  } catch (e) {
    console.error("Exception in createEvent:", e);

    // En cas d'erreur, essayer d'ajouter à la queue hors-ligne
    if (!networkService.isConnected()) {
      const eventPayload: EventInsert = {
        ...eventData,
        organizer_id: organizerId,
      };

      const tempId = Date.now();
      const tempEvent: Event = {
        ...eventPayload,
        id: tempId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      await offlineQueue.add("CREATE", "event", eventPayload, {
        priority: "high",
        userId: organizerId,
      });

      await cache.invalidate(CACHE_KEYS.EVENTS);
      return tempEvent;
    }

    if (e instanceof Error) throw e;
    else throw new Error("Erreur lors de la création de l'événement");
  }
}

export async function updateEvent(
  eventId: number,
  updates: EventUpdate
): Promise<Event | null> {
  const { id, organizer_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  try {
    const { data, error } = await supabase
      .from("events")
      .update(updatePayload)
      .eq("id", eventId)
      .select()
      .single();
    if (error) {
      handleError(error, "updateEvent");
      return null;
    }
    return data;
  } catch (e) {
    console.error("Exception in updateEvent:", e);
    return null;
  }
}

export async function deleteEvent(eventId: number): Promise<boolean> {
  try {
    const { error } = await supabase.from("events").delete().eq("id", eventId);
    if (error) {
      handleError(error, "deleteEvent");
      return false;
    }
    return true;
  } catch (e) {
    console.error("Exception in deleteEvent:", e);
    return false;
  }
}

// --- Participants ---

// Let function return type be inferred from query + generated types
export async function fetchParticipantsForEvent(eventId: number) {
  try {
    // Utiliser uniquement le client standard
    const { data, error } = await supabase
      .from("participants")
      .select(`*, profiles ( name, avatar_url )`)
      .eq("event_id", eventId)
      .order("role", { ascending: true })
      .order("created_at", { ascending: true });

    if (error) {
      handleError(error, "fetchParticipantsForEvent");
      return [];
    }

    // Ajouter name_display helper après récupération
    return (data || []).map((p) => ({
      ...p,
      // Vérifier que profiles n'est pas null avant d'accéder au nom
      name_display: p.user_id ? p.profiles?.name ?? null : p.anonymous_name,
    }));
  } catch (e) {
    console.error("Exception in fetchParticipantsForEvent:", e);
    return [];
  }
}

export async function createParticipant(
  participantData: ParticipantInsert
): Promise<Participant | null> {
  // Return generated Participant Row type
  if (
    !participantData.event_id ||
    (!participantData.user_id && !participantData.anonymous_name)
  ) {
    console.error("Missing required fields for participant");
    return null;
  }
  const insertData: ParticipantInsert = { ...participantData };
  if (insertData.user_id) {
    insertData.anonymous_name = null;
    insertData.anonymous_email = null;
    insertData.anonymous_phone = null;
  }
  try {
    // Utiliser uniquement le client standard
    const { data, error } = await supabase
      .from("participants")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      handleError(error, `createParticipant`);
      return null;
    }

    // Déclencher les achievements pour la participation
    if (data && participantData.user_id) {
      try {
        await achievements.updateStats(participantData.user_id, {
          events_participated: 1,
        });
        await achievements.checkAchievements(participantData.user_id);
      } catch (achievementError) {
        console.warn(
          "Error updating achievements for event participation:",
          achievementError
        );
      }
    }

    return data;
  } catch (e) {
    console.error("Exception in createParticipant:", e);
    return null;
  }
}

export async function updateParticipant(
  participantId: number,
  updates: ParticipantUpdate
): Promise<Participant | null> {
  // Return generated Participant Row type
  const { id, event_id, user_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("participants")
    .update(updatePayload)
    .eq("id", participantId)
    .select()
    .single();
  handleError(error, "updateParticipant");
  return data;
}

export async function updateParticipantStatus(
  participantId: number,
  status: ParticipantStatusEnum
): Promise<Participant | null> {
  return updateParticipant(participantId, { status });
}

export async function deleteParticipant(
  participantId: number
): Promise<boolean> {
  const { error } = await supabase
    .from("participants")
    .delete()
    .eq("id", participantId);
  handleError(error, "deleteParticipant");
  return !error;
}

export async function fetchMyParticipantRecord(
  eventId: number,
  userId: string
): Promise<Participant | null> {
  // Return generated Participant Row type
  // Utiliser le client standard
  const { data, error } = await supabase
    .from("participants")
    .select("*")
    .eq("event_id", eventId)
    .eq("user_id", userId)
    .maybeSingle();
  handleError(error, "fetchMyParticipantRecord");
  return data;
}

export async function leaveEvent(
  eventId: number,
  userId: string
): Promise<boolean> {
  try {
    // Récupérer d'abord le participant pour vérifier qu'il n'est pas organisateur
    const participant = await fetchMyParticipantRecord(eventId, userId);

    if (!participant) {
      throw new Error("Vous n'êtes pas participant de cet événement");
    }

    if (participant.role === "organizer") {
      throw new Error(
        "L'organisateur ne peut pas quitter son propre événement"
      );
    }

    // Supprimer le participant
    const success = await deleteParticipant(participant.id);
    return success;
  } catch (error) {
    console.error("Error leaving event:", error);
    throw error;
  }
}

// --- Items ---

// Let function return type be inferred from query + generated types
export async function fetchItemsForEvent(eventId: number) {
  try {
    // Utiliser le client standard
    const { data, error } = await supabase
      .from("items")
      .select(
        `
        *,
        suggester:profiles!suggester_id ( name, avatar_url ),
        assigned_participant:participants!assigned_participant_id (
            id, user_id, anonymous_name,
            profiles ( name, avatar_url )
        ),
        fixed_by_participant:participants!fixed_by_participant_id (
            id, user_id, anonymous_name,
            profiles ( name, avatar_url )
        )
      `
      )
      .eq("event_id", eventId)
      .order("category")
      .order("created_at");

    handleError(error, "fetchItemsForEvent");
    if (!data) return [];

    // Ajouter name_display helper aux participants imbriqués de manière sécurisée
    return data.map((item: any) => ({
      ...item,
      assigned_participant: item.assigned_participant
        ? {
            ...item.assigned_participant,
            name_display: item.assigned_participant.user_id
              ? item.assigned_participant.profiles?.name ?? null
              : item.assigned_participant.anonymous_name,
          }
        : null,
      fixed_by_participant: item.fixed_by_participant
        ? {
            ...item.fixed_by_participant,
            name_display: item.fixed_by_participant.user_id
              ? item.fixed_by_participant.profiles?.name ?? null
              : item.fixed_by_participant.anonymous_name,
          }
        : null,
    }));
  } catch (e) {
    console.error("Exception in fetchItemsForEvent:", e);
    return [];
  }
}

// Let function return type be inferred
export async function fetchItemsAssignedToUser(userId: string) {
  try {
    // Approche simplifiée : requête directe sur la table items avec client standard
    const { data: items, error: itemsError } = await supabase
      .from("items")
      .select(
        `
        *,
        events!event_id (
          id, title, icon, date_time
        )
      `
      )
      .not("assigned_participant_id", "is", null); // Seulement les items assignés

    if (itemsError) {
      handleError(itemsError, "fetchItemsAssignedToUser - direct items");
      // Fallback: retourner un tableau vide pour éviter de casser l'app
      return [];
    }

    if (!items || items.length === 0) {
      return [];
    }

    // Filtrer les items en vérifiant si le participant assigné appartient à l'utilisateur
    const userItems = [];

    for (const item of items) {
      if (item.assigned_participant_id) {
        try {
          const { data: participant } = await supabase
            .from("participants")
            .select("user_id")
            .eq("id", item.assigned_participant_id)
            .single();

          if (participant && participant.user_id === userId) {
            userItems.push(item);
          }
        } catch (participantError) {
          // Ignorer cet item si on ne peut pas vérifier le participant
        }
      }
    }

    return userItems;
  } catch (e) {
    console.error("Exception in fetchItemsAssignedToUser:", e);
    return [];
  }
}

export async function createItem(itemData: ItemInsert): Promise<Item | null> {
  // Return generated Item Row type
  // Utiliser le client standard
  const { data, error } = await supabase
    .from("items")
    .insert(itemData)
    .select()
    .single();

  handleError(error, "createItem");

  // Déclencher les achievements pour la gestion d'items
  if (data && itemData.created_by) {
    try {
      await achievements.updateStats(itemData.created_by, { items_managed: 1 });
      await achievements.checkAchievements(itemData.created_by);
    } catch (achievementError) {
      console.warn(
        "Error updating achievements for item creation:",
        achievementError
      );
    }
  }

  return data;
}

export async function updateItem(
  itemId: number,
  updates: ItemUpdate
): Promise<Item | null> {
  try {
    // Return generated Item Row type
    const { id, event_id, suggester_id, created_at, ...validUpdates } = updates;

    // Nettoyer les valeurs pour éviter les erreurs de syntaxe bigint
    const cleanedUpdates = { ...validUpdates };

    // S'assurer que les IDs de participants sont des nombres valides ou null
    if ("assigned_participant_id" in cleanedUpdates) {
      const participantId = cleanedUpdates.assigned_participant_id;
      cleanedUpdates.assigned_participant_id =
        participantId === null || participantId === undefined
          ? null
          : Number(participantId);
    }

    if ("fixed_by_participant_id" in cleanedUpdates) {
      const participantId = cleanedUpdates.fixed_by_participant_id;
      cleanedUpdates.fixed_by_participant_id =
        participantId === null || participantId === undefined
          ? null
          : Number(participantId);
    }

    const updatePayload = {
      ...cleanedUpdates,
      updated_at: new Date().toISOString(),
    };

    console.log("updateItem payload:", updatePayload);

    // Utiliser le client standard
    const { data, error } = await supabase
      .from("items")
      .update(updatePayload)
      .eq("id", itemId)
      .select()
      .single();

    if (error) {
      handleError(error, "updateItem");
      return null;
    }

    return data;
  } catch (e) {
    console.error("Exception in updateItem:", e);
    return null;
  }
}

export async function deleteItem(itemId: number): Promise<boolean> {
  // Utiliser le client standard
  const { error } = await supabase.from("items").delete().eq("id", itemId);
  handleError(error, "deleteItem");
  return !error;
}

export async function assignItemToParticipant(
  itemId: number,
  participantId: number | null
): Promise<Item | null> {
  return updateItem(itemId, { assigned_participant_id: participantId });
}

export async function fixItemForParticipant(
  itemId: number,
  participantId: number
): Promise<Item | null> {
  return updateItem(itemId, { fixed_by_participant_id: participantId });
}

export async function unfixItem(itemId: number): Promise<Item | null> {
  return updateItem(itemId, { fixed_by_participant_id: null });
}

// ===== FONCTIONS FINANCIÈRES =====

/**
 * Met à jour le coût réel d'un item
 */
export async function updateItemCost(
  itemId: number,
  actualCost: number,
  paidByParticipantId: number | null = null
): Promise<Item | null> {
  try {
    const { data, error } = await supabase
      .from("items")
      .update({
        actual_cost: actualCost,
        paid_by_participant_id: paidByParticipantId,
        updated_at: new Date().toISOString(),
      })
      .eq("id", itemId)
      .select(
        `
        *,
        assigned_participant:participants!assigned_participant_id(id, anonymous_name, user_id, profiles(name)),
        fixed_by_participant:participants!fixed_by_participant_id(id, anonymous_name, user_id, profiles(name))
      `
      )
      .single();

    handleError(error, "updateItemCost");
    return data;
  } catch (error) {
    console.error("Error updating item cost:", error);
    return null;
  }
}

/**
 * Récupère le résumé financier d'un événement
 */
export async function fetchEventFinancialSummary(eventId: number) {
  try {
    const { data, error } = await supabase
      .from("financial_summaries")
      .select(
        `
        *,
        participant:participants(
          id,
          anonymous_name,
          user_id,
          role,
          status,
          profiles(name, avatar_url)
        )
      `
      )
      .eq("event_id", eventId)
      .order("net_balance", { ascending: false });

    handleError(error, "fetchEventFinancialSummary");
    return data || [];
  } catch (error) {
    console.error("Error fetching financial summary:", error);
    return [];
  }
}

/**
 * Recalcule les résumés financiers d'un événement
 */
export async function recalculateEventFinancials(
  eventId: number
): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin.rpc(
      "recalculate_event_financial_summaries",
      {
        p_event_id: eventId,
      }
    );

    handleError(error, "recalculateEventFinancials");
    return !error;
  } catch (error) {
    console.error("Error recalculating event financials:", error);
    return false;
  }
}

/**
 * Crée une transaction financière
 */
export async function createFinancialTransaction(
  eventId: number,
  fromParticipantId: number,
  toParticipantId: number,
  amount: number,
  description?: string
) {
  try {
    const { data, error } = await supabase
      .from("financial_transactions")
      .insert({
        event_id: eventId,
        from_participant_id: fromParticipantId,
        to_participant_id: toParticipantId,
        amount,
        description: description || null,
        is_settled: false,
      })
      .select(
        `
        *,
        from_participant:participants!from_participant_id(
          id, anonymous_name, user_id, profiles(name)
        ),
        to_participant:participants!to_participant_id(
          id, anonymous_name, user_id, profiles(name)
        )
      `
      )
      .single();

    handleError(error, "createFinancialTransaction");
    return data;
  } catch (error) {
    console.error("Error creating financial transaction:", error);
    return null;
  }
}

/**
 * Marque une transaction comme réglée
 */
export async function settleFinancialTransaction(
  transactionId: number
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from("financial_transactions")
      .update({
        is_settled: true,
        settled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq("id", transactionId);

    handleError(error, "settleFinancialTransaction");
    return !error;
  } catch (error) {
    console.error("Error settling financial transaction:", error);
    return false;
  }
}

/**
 * Récupère les transactions financières d'un événement
 */
export async function fetchEventFinancialTransactions(eventId: number) {
  try {
    const { data, error } = await supabase
      .from("financial_transactions")
      .select(
        `
        *,
        from_participant:participants!from_participant_id(
          id, anonymous_name, user_id, role, profiles(name, avatar_url)
        ),
        to_participant:participants!to_participant_id(
          id, anonymous_name, user_id, role, profiles(name, avatar_url)
        )
      `
      )
      .eq("event_id", eventId)
      .order("created_at", { ascending: false });

    handleError(error, "fetchEventFinancialTransactions");
    return data || [];
  } catch (error) {
    console.error("Error fetching financial transactions:", error);
    return [];
  }
}

// --- Contacts ---

// Let function return type be inferred
export async function fetchContacts(userId: string) {
  const { data, error } = await supabase
    .from("contacts")
    .select(`*, contact_profile:contact_profile_id ( name, avatar_url )`)
    .eq("user_id", userId)
    .order("name");
  handleError(error, "fetchContacts");
  return data || [];
}

export async function createContact(
  contactData: ContactInsert
): Promise<Contact | null> {
  // Return generated Contact Row type
  const { data, error } = await supabase
    .from("contacts")
    .insert(contactData)
    .select()
    .single();
  handleError(error, "createContact");
  return data;
}

export async function updateContact(
  contactId: number,
  updates: ContactUpdate
): Promise<Contact | null> {
  // Return generated Contact Row type
  const { id, user_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("contacts")
    .update(updatePayload)
    .eq("id", contactId)
    .select()
    .single();
  handleError(error, "updateContact");
  return data;
}

export async function deleteContact(contactId: number): Promise<boolean> {
  const { error } = await supabase
    .from("contacts")
    .delete()
    .eq("id", contactId);
  handleError(error, "deleteContact");
  return !error;
}

// --- Contact Groups ---

export async function fetchContactGroups(
  userId: string
): Promise<ContactGroup[]> {
  // Return generated ContactGroup Row type
  const { data, error } = await supabase
    .from("contact_groups")
    .select("*")
    .eq("user_id", userId)
    .order("name");
  handleError(error, "fetchContactGroups");
  return data || [];
}

export async function createContactGroup(
  groupData: ContactGroupInsert
): Promise<ContactGroup | null> {
  // Return generated ContactGroup Row type
  const { data, error } = await supabase
    .from("contact_groups")
    .insert(groupData)
    .select()
    .single();
  handleError(error, "createContactGroup");
  return data;
}

export async function updateContactGroup(
  groupId: number,
  updates: ContactGroupUpdate
): Promise<ContactGroup | null> {
  // Return generated ContactGroup Row type
  const { id, user_id, created_at, ...validUpdates } = updates;
  const updatePayload = {
    ...validUpdates,
    updated_at: new Date().toISOString(),
  };
  const { data, error } = await supabase
    .from("contact_groups")
    .update(updatePayload)
    .eq("id", groupId)
    .select()
    .single();
  handleError(error, "updateContactGroup");
  return data;
}

export async function deleteContactGroup(groupId: number): Promise<boolean> {
  const { error } = await supabase
    .from("contact_groups")
    .delete()
    .eq("id", groupId);
  handleError(error, "deleteContactGroup");
  return !error;
}

// --- Contact Group Members ---

export async function addContactToGroup(
  groupId: number,
  contactId: number
): Promise<boolean> {
  const payload: ContactGroupMemberInsert = {
    group_id: groupId,
    contact_id: contactId,
  };
  const { error } = await supabase
    .from("contact_group_members")
    .insert(payload);
  handleError(error, "addContactToGroup");
  return !error;
}

export async function removeContactFromGroup(
  groupId: number,
  contactId: number
): Promise<boolean> {
  const { error } = await supabase
    .from("contact_group_members")
    .delete()
    .match({ group_id: groupId, contact_id: contactId });
  handleError(error, "removeContactFromGroup");
  return !error;
}

// Let function return type be inferred
export async function fetchContactsInGroup(groupId: number) {
  const { data, error } = await supabase
    .from("contact_group_members")
    .select(
      `
      contact_id, group_id,
      contacts!inner(
        *,
        contact_profile:contact_profile_id ( name, avatar_url )
      )
      `
    )
    .eq("group_id", groupId);

  handleError(error, "fetchContactsInGroup");
  // Map to extract the nested contact data, ensuring type safety
  // The inferred type of `data` will be complex, adjust component usage accordingly
  return data?.map((item) => item.contacts).filter(Boolean) || [];
}

// --- Messages --- (Commented out)
/* ... */
// Fonction pour récupérer un événement par token d'invitation
export async function fetchEventByInvitationToken(token: string) {
  try {
    const { data, error } = await supabase
      .from("participants")
      .select(
        `
        event_id,
        events (*)
      `
      )
      .eq("invitation_token", token)
      .single();

    if (error) {
      console.error("Error fetching event by invitation token:", error);
      return null;
    }

    return data?.events as Event | null;
  } catch (error) {
    console.error("Error in fetchEventByInvitationToken:", error);
    return null;
  }
}

// Fonction pour récupérer un participant par token d'invitation
export async function fetchParticipantByToken(token: string) {
  try {
    const { data, error } = await supabase
      .from("participants")
      .select("*")
      .eq("invitation_token", token)
      .single();

    if (error) {
      console.error("Error fetching participant by token:", error);
      return null;
    }

    return data as Participant | null;
  } catch (error) {
    console.error("Error in fetchParticipantByToken:", error);
    return null;
  }
}

// --- Supabase RLS Fix ---

// Fonction pour désactiver temporairement RLS sur les tables problématiques
export async function disableRLSTemporarily(): Promise<boolean> {
  try {
    console.log("🔧 Désactivation temporaire de RLS...");

    // Utiliser le client standard pour exécuter les commandes SQL
    // Note: Ces commandes nécessitent des privilèges élevés
    const commands = [
      "ALTER TABLE public.events DISABLE ROW LEVEL SECURITY;",
      "ALTER TABLE public.participants DISABLE ROW LEVEL SECURITY;",
      "ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;",
      "ALTER TABLE public.items DISABLE ROW LEVEL SECURITY;",
    ];

    for (const command of commands) {
      try {
        // Essayer d'exécuter via une fonction RPC si elle existe
        const { error } = await supabase.rpc("exec_sql", { sql: command });
        if (error && !error.message.includes("does not exist")) {
          console.warn("Commande SQL échouée:", command, error.message);
        }
      } catch (e) {
        console.warn("Impossible d'exécuter la commande SQL:", command);
      }
    }

    console.log("✅ RLS désactivé temporairement (si autorisé)");
    console.log(
      "ℹ️ Si les erreurs persistent, exécutez manuellement le script supabase/sql/disable_rls_temporarily.sql"
    );
    return true;
  } catch (e) {
    console.error("Exception lors de la désactivation de RLS:", e);
    return false;
  }
}

// --- End lib/supabaseCrud.ts ---
