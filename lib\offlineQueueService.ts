/**
 * Service de queue d'actions hors-ligne pour Party Organizer
 * Gère les actions en attente de synchronisation
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { networkService } from './networkService';

export interface QueueAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  entity: 'event' | 'item' | 'participant' | 'transaction' | 'notification';
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  eventId?: number;
  dependsOn?: string[]; // IDs d'autres actions dont celle-ci dépend
}

export interface QueueStats {
  totalActions: number;
  pendingActions: number;
  failedActions: number;
  completedActions: number;
  lastSync?: Date;
  nextSync?: Date;
}

class OfflineQueueService {
  private queue: QueueAction[] = [];
  private processing = false;
  private syncInterval?: NodeJS.Timeout;
  private readonly QUEUE_KEY = 'offline_queue';
  private readonly SYNC_INTERVAL = 30000; // 30 secondes

  constructor() {
    this.initialize();
  }

  /**
   * Initialise le service de queue
   */
  private async initialize() {
    try {
      // Charger la queue depuis le stockage
      await this.loadQueue();

      // Écouter les changements de réseau
      networkService.addListener({
        onConnected: () => this.processQueue(),
        onDisconnected: () => this.stopProcessing(),
        onReconnected: () => this.processQueue(),
        onSlowConnection: () => {}, // Ne rien faire pour connexion lente
      });

      // Démarrer le traitement si connecté
      if (networkService.isConnected()) {
        this.startPeriodicSync();
      }

      console.log('OfflineQueueService initialized');
    } catch (error) {
      console.error('Error initializing OfflineQueueService:', error);
    }
  }

  /**
   * Charge la queue depuis le stockage persistant
   */
  private async loadQueue() {
    try {
      const stored = await AsyncStorage.getItem(this.QUEUE_KEY);
      if (stored) {
        this.queue = JSON.parse(stored);
        console.log(`Loaded ${this.queue.length} actions from offline queue`);
      }
    } catch (error) {
      console.error('Error loading offline queue:', error);
      this.queue = [];
    }
  }

  /**
   * Sauvegarde la queue dans le stockage persistant
   */
  private async saveQueue() {
    try {
      await AsyncStorage.setItem(this.QUEUE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      console.error('Error saving offline queue:', error);
    }
  }

  /**
   * Génère un ID unique pour une action
   */
  private generateActionId(): string {
    return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Ajoute une action à la queue
   */
  async addAction(
    type: QueueAction['type'],
    entity: QueueAction['entity'],
    data: any,
    options: {
      priority?: QueueAction['priority'];
      maxRetries?: number;
      userId?: string;
      eventId?: number;
      dependsOn?: string[];
    } = {}
  ): Promise<string> {
    const action: QueueAction = {
      id: this.generateActionId(),
      type,
      entity,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: options.maxRetries || 3,
      priority: options.priority || 'medium',
      userId: options.userId,
      eventId: options.eventId,
      dependsOn: options.dependsOn || [],
    };

    // Insérer selon la priorité
    const insertIndex = this.findInsertIndex(action);
    this.queue.splice(insertIndex, 0, action);

    await this.saveQueue();

    console.log(`Action added to queue: ${action.type} ${action.entity} (${action.id})`);

    // Traiter immédiatement si connecté
    if (networkService.isConnected() && !this.processing) {
      this.processQueue();
    }

    return action.id;
  }

  /**
   * Trouve l'index d'insertion selon la priorité
   */
  private findInsertIndex(action: QueueAction): number {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    const actionPriority = priorityOrder[action.priority];

    for (let i = 0; i < this.queue.length; i++) {
      const queuePriority = priorityOrder[this.queue[i].priority];
      if (actionPriority < queuePriority) {
        return i;
      }
    }

    return this.queue.length;
  }

  /**
   * Traite la queue d'actions
   */
  async processQueue(): Promise<void> {
    if (this.processing || !networkService.isConnected()) {
      return;
    }

    this.processing = true;
    console.log(`Processing offline queue: ${this.queue.length} actions`);

    try {
      const actionsToProcess = [...this.queue];
      
      for (const action of actionsToProcess) {
        // Vérifier les dépendances
        if (!this.areDependenciesSatisfied(action)) {
          continue;
        }

        try {
          await this.executeAction(action);
          
          // Supprimer l'action réussie
          this.removeAction(action.id);
          console.log(`Action completed: ${action.id}`);
          
        } catch (error) {
          console.error(`Action failed: ${action.id}`, error);
          
          // Incrémenter le compteur de retry
          action.retryCount++;
          
          if (action.retryCount >= action.maxRetries) {
            console.error(`Action max retries reached: ${action.id}`);
            // Optionnel: déplacer vers une queue d'échecs
            this.removeAction(action.id);
          }
        }
      }

      await this.saveQueue();
    } finally {
      this.processing = false;
    }
  }

  /**
   * Vérifie si les dépendances d'une action sont satisfaites
   */
  private areDependenciesSatisfied(action: QueueAction): boolean {
    if (!action.dependsOn || action.dependsOn.length === 0) {
      return true;
    }

    // Vérifier que toutes les dépendances ont été traitées
    return action.dependsOn.every(depId => 
      !this.queue.find(a => a.id === depId)
    );
  }

  /**
   * Exécute une action spécifique
   */
  private async executeAction(action: QueueAction): Promise<void> {
    // Importer dynamiquement les services pour éviter les dépendances circulaires
    const { createEvent, updateEvent, deleteEvent } = await import('./supabaseCrud');
    const { createItem, updateItem, deleteItem } = await import('./supabaseCrud');
    const { createParticipant, updateParticipant, deleteParticipant } = await import('./supabaseCrud');

    switch (action.entity) {
      case 'event':
        await this.executeEventAction(action);
        break;
      case 'item':
        await this.executeItemAction(action);
        break;
      case 'participant':
        await this.executeParticipantAction(action);
        break;
      case 'transaction':
        await this.executeTransactionAction(action);
        break;
      case 'notification':
        await this.executeNotificationAction(action);
        break;
      default:
        throw new Error(`Unknown entity type: ${action.entity}`);
    }
  }

  /**
   * Exécute une action sur un événement
   */
  private async executeEventAction(action: QueueAction): Promise<void> {
    const { createEvent, updateEvent, deleteEvent } = await import('./supabaseCrud');

    switch (action.type) {
      case 'CREATE':
        await createEvent(action.data);
        break;
      case 'UPDATE':
        await updateEvent(action.data.id, action.data);
        break;
      case 'DELETE':
        await deleteEvent(action.data.id);
        break;
    }
  }

  /**
   * Exécute une action sur un item
   */
  private async executeItemAction(action: QueueAction): Promise<void> {
    const { createItem, updateItem, deleteItem } = await import('./supabaseCrud');

    switch (action.type) {
      case 'CREATE':
        await createItem(action.data);
        break;
      case 'UPDATE':
        await updateItem(action.data.id, action.data);
        break;
      case 'DELETE':
        await deleteItem(action.data.id);
        break;
    }
  }

  /**
   * Exécute une action sur un participant
   */
  private async executeParticipantAction(action: QueueAction): Promise<void> {
    const { createParticipant, updateParticipant, deleteParticipant } = await import('./supabaseCrud');

    switch (action.type) {
      case 'CREATE':
        await createParticipant(action.data);
        break;
      case 'UPDATE':
        await updateParticipant(action.data.id, action.data);
        break;
      case 'DELETE':
        await deleteParticipant(action.data.id);
        break;
    }
  }

  /**
   * Exécute une action sur une transaction
   */
  private async executeTransactionAction(action: QueueAction): Promise<void> {
    // À implémenter selon vos besoins
    console.log('Transaction action not implemented yet');
  }

  /**
   * Exécute une action sur une notification
   */
  private async executeNotificationAction(action: QueueAction): Promise<void> {
    // À implémenter selon vos besoins
    console.log('Notification action not implemented yet');
  }

  /**
   * Supprime une action de la queue
   */
  private removeAction(actionId: string): void {
    const index = this.queue.findIndex(action => action.id === actionId);
    if (index > -1) {
      this.queue.splice(index, 1);
    }
  }

  /**
   * Démarre la synchronisation périodique
   */
  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      if (networkService.isConnected() && this.queue.length > 0) {
        this.processQueue();
      }
    }, this.SYNC_INTERVAL);
  }

  /**
   * Arrête le traitement
   */
  private stopProcessing(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = undefined;
    }
  }

  /**
   * Obtient les statistiques de la queue
   */
  getStats(): QueueStats {
    const totalActions = this.queue.length;
    const failedActions = this.queue.filter(a => a.retryCount >= a.maxRetries).length;
    const pendingActions = totalActions - failedActions;

    return {
      totalActions,
      pendingActions,
      failedActions,
      completedActions: 0, // À implémenter avec un compteur persistant
      lastSync: undefined, // À implémenter
      nextSync: this.syncInterval ? new Date(Date.now() + this.SYNC_INTERVAL) : undefined,
    };
  }

  /**
   * Vide la queue (pour les tests ou reset)
   */
  async clearQueue(): Promise<void> {
    this.queue = [];
    await this.saveQueue();
    console.log('Offline queue cleared');
  }

  /**
   * Force la synchronisation
   */
  async forceSync(): Promise<void> {
    if (networkService.isConnected()) {
      await this.processQueue();
    } else {
      throw new Error('Cannot sync while offline');
    }
  }
}

// Instance singleton
export const offlineQueueService = new OfflineQueueService();

// Fonctions utilitaires
export const offlineQueue = {
  add: offlineQueueService.addAction.bind(offlineQueueService),
  process: offlineQueueService.processQueue.bind(offlineQueueService),
  getStats: offlineQueueService.getStats.bind(offlineQueueService),
  clear: offlineQueueService.clearQueue.bind(offlineQueueService),
  forceSync: offlineQueueService.forceSync.bind(offlineQueueService),
};
