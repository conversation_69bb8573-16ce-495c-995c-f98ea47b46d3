import React, { useState, useEffect } from "react";
import { View, FlatList, RefreshControl, Alert } from "react-native";
import { useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { H1 } from "~/components/ui/typography";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";

// Icônes temporairement supprimées pour compatibilité web
import { useAuth } from "~/lib/AuthContext";
import { fetchEventsForUser } from "~/lib/supabaseCrud";
import { Event } from "~/lib/types";
import { useOptimizedEvents, useDataPreloader } from "~/hooks/useOptimizedData";
import { NetworkStatusIndicator } from "~/components/NetworkStatusIndicator";
import { useDashboardStats } from "~/hooks/useDashboard";
import { useOnboardingProgress } from "~/hooks/useOnboarding";
import { useUserStats } from "~/hooks/useAchievements";

export default function EventsScreen() {
  const router = useRouter();
  const { session, isAnonymous } = useAuth();

  // Utiliser les hooks optimisés
  const { events, loading, error, refreshing, refresh } = useOptimizedEvents();
  const { preloaded } = useDataPreloader();

  // Utiliser les statistiques du dashboard pour les organisateurs
  const { stats: dashboardStats } = useDashboardStats();

  // Utiliser la progression de l'onboarding
  const { progress: onboardingProgress } = useOnboardingProgress();

  // Utiliser les stats d'achievements
  const { stats: achievementStats } = useUserStats();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("fr-FR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderEventCard = ({ item: event }: { item: Event }) => (
    <Card className="mb-4 border border-border bg-card">
      <CardHeader className="pb-2">
        <View className="flex-row items-start">
          <View className="flex-row items-center flex-1 pr-2">
            <Text className="text-3xl mr-3">{event.icon || "🎉"}</Text>
            <View className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold text-foreground">
                {event.title}
              </CardTitle>
              {event.description && (
                <Text className="text-sm text-muted-foreground mt-1">
                  {event.description}
                </Text>
              )}
            </View>
          </View>
          <View className="w-[50px] items-end">
            <View
              className={`px-2 py-1 rounded-md ${
                event.organizer_id === session?.user?.id
                  ? "bg-yellow-500/20 border border-yellow-500/30"
                  : "bg-muted"
              }`}
            >
              <Text
                className={`text-xs font-medium ${
                  event.organizer_id === session?.user?.id
                    ? "text-yellow-600"
                    : "text-muted-foreground"
                }`}
              >
                {event.organizer_id === session?.user?.id ? "Orga" : "Invité"}
              </Text>
            </View>
          </View>
        </View>
      </CardHeader>
      <CardContent className="gap-2">
        <View className="flex-row items-center gap-2">
          <Text className="text-muted-foreground">📅</Text>
          <Text className="text-sm text-foreground">
            {formatDate(event.date_time)}
          </Text>
        </View>
        <View className="flex-row items-center gap-2">
          <Text className="text-muted-foreground">🕐</Text>
          <Text className="text-sm text-foreground">
            {formatTime(event.date_time)}
          </Text>
        </View>
        {event.location && (
          <View className="flex-row items-center gap-2">
            <Text className="text-muted-foreground">📍</Text>
            <Text className="text-xs text-muted-foreground">
              {event.location}
            </Text>
          </View>
        )}
        <View className="flex-row items-center justify-between mt-3">
          <View className="flex-row items-center gap-2">
            <Text className="text-muted-foreground">👥</Text>
            <Text className="text-sm text-muted-foreground">Participants</Text>
          </View>
          <Button
            variant="outline"
            size="sm"
            onPress={() => router.push(`/event/${event.id}`)}
          >
            <Text>Voir détails</Text>
          </Button>
        </View>
      </CardContent>
    </Card>
  );

  if (!session) {
    return (
      <View className="flex-1 justify-center items-center p-4 bg-background">
        <Text className="text-lg text-muted-foreground text-center">
          Chargement...
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background">
      <View className="p-4 pb-2">
        {/* Indicateur de statut réseau */}
        <View className="mb-4">
          <NetworkStatusIndicator showDetails={false} />
        </View>

        {/* Widget progression onboarding */}
        {!isAnonymous &&
          !onboardingProgress.isCompleted &&
          onboardingProgress.totalSteps > 0 && (
            <Card className="mb-4 border border-primary/30 bg-primary/5">
              <CardContent className="p-4">
                <View className="flex-row items-center justify-between mb-3">
                  <Text className="font-semibold">
                    🎯 Terminez votre configuration
                  </Text>
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => router.push("/onboarding/tutorial")}
                  >
                    <Text className="text-xs">Continuer →</Text>
                  </Button>
                </View>

                <View className="mb-3">
                  <View className="flex-row items-center justify-between mb-1">
                    <Text className="text-sm text-muted-foreground">
                      {onboardingProgress.completedSteps}/
                      {onboardingProgress.totalSteps} étapes
                    </Text>
                    <Text className="text-sm text-muted-foreground">
                      {onboardingProgress.progressPercentage.toFixed(0)}%
                    </Text>
                  </View>
                  <View className="w-full h-2 bg-muted rounded-full">
                    <View
                      className="h-full bg-primary rounded-full transition-all duration-300"
                      style={{
                        width: `${onboardingProgress.progressPercentage}%`,
                      }}
                    />
                  </View>
                </View>

                {onboardingProgress.nextStep && (
                  <Text className="text-xs text-muted-foreground">
                    Prochaine étape : {onboardingProgress.nextStep.title}
                  </Text>
                )}
              </CardContent>
            </Card>
          )}

        {/* Widget achievements rapide */}
        {!isAnonymous && achievementStats && onboardingProgress.isCompleted && (
          <Card className="mb-4 border border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
            <CardContent className="p-4">
              <View className="flex-row items-center justify-between mb-3">
                <Text className="font-semibold">🏆 Vos achievements</Text>
                <Button
                  variant="ghost"
                  size="sm"
                  onPress={() => router.push("/(tabs)/achievements")}
                >
                  <Text className="text-xs">Voir tout →</Text>
                </Button>
              </View>

              <View className="flex-row justify-between">
                <View className="items-center">
                  <Text className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                    {achievementStats.level}
                  </Text>
                  <Text className="text-xs text-muted-foreground">Niveau</Text>
                </View>
                <View className="items-center">
                  <Text className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                    {achievementStats.total_points}
                  </Text>
                  <Text className="text-xs text-muted-foreground">Points</Text>
                </View>
                <View className="items-center">
                  <Text className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                    {achievementStats.achievements_unlocked}
                  </Text>
                  <Text className="text-xs text-muted-foreground">Badges</Text>
                </View>
                <View className="items-center">
                  <Text className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                    {achievementStats.events_organized}
                  </Text>
                  <Text className="text-xs text-muted-foreground">
                    Événements
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card>
        )}

        {/* Widget statistiques rapides pour les organisateurs */}
        {!isAnonymous &&
          dashboardStats &&
          onboardingProgress.isCompleted &&
          !achievementStats && (
            <Card className="mb-4 border border-border">
              <CardContent className="p-4">
                <View className="flex-row items-center justify-between mb-3">
                  <Text className="font-semibold">📊 Vos statistiques</Text>
                  <Button
                    variant="ghost"
                    size="sm"
                    onPress={() => router.push("/(tabs)/dashboard")}
                  >
                    <Text className="text-xs">Voir tout →</Text>
                  </Button>
                </View>

                <View className="flex-row justify-between">
                  <View className="items-center">
                    <Text className="text-lg font-bold">
                      {dashboardStats.totalEvents}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      Événements
                    </Text>
                  </View>
                  <View className="items-center">
                    <Text className="text-lg font-bold">
                      {dashboardStats.upcomingEvents}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      À venir
                    </Text>
                  </View>
                  <View className="items-center">
                    <Text className="text-lg font-bold">
                      {dashboardStats.totalParticipants}
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      Participants
                    </Text>
                  </View>
                  <View className="items-center">
                    <Text className="text-lg font-bold">
                      {dashboardStats.totalSpent.toFixed(0)}€
                    </Text>
                    <Text className="text-xs text-muted-foreground">
                      Dépensés
                    </Text>
                  </View>
                </View>

                {dashboardStats.hasAlerts && (
                  <View className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                    <Text className="text-xs text-red-700 dark:text-red-300">
                      🚨 Vous avez des alertes importantes
                    </Text>
                  </View>
                )}
              </CardContent>
            </Card>
          )}

        {isAnonymous && (
          <View className="mb-4 p-3 bg-muted border border-border rounded-lg">
            <Text className="text-sm text-foreground text-center">
              👋 Mode invité activé ! Vous pouvez rejoindre des événements et
              créer un compte plus tard.
            </Text>
          </View>
        )}
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <H1 className="text-2xl font-bold text-foreground">
              {isAnonymous ? "Événements" : "Mes Événements"}
            </H1>
            <Text className="text-muted-foreground">
              {events.length} événement{events.length !== 1 ? "s" : ""}
            </Text>
          </View>
          <View className="flex-row gap-2">
            <Button
              variant="outline"
              size="sm"
              onPress={() => router.push("/join-event")}
            >
              <Text className="text-foreground text-xs">🔗 Rejoindre</Text>
            </Button>
            <Button onPress={() => router.push("/event/create")} size="sm">
              <Text className="text-primary-foreground text-xs">➕</Text>
            </Button>
          </View>
        </View>
      </View>

      {events.length === 0 ? (
        <View className="flex-1 justify-center items-center gap-4 p-4">
          <View className="bg-muted/20 p-6 rounded-full">
            <Text className="text-6xl">📅</Text>
          </View>
          <View className="items-center gap-2">
            <Text className="text-xl font-semibold text-foreground">
              Aucun événement
            </Text>
            <Text className="text-muted-foreground text-center max-w-sm">
              Créez votre premier événement ou rejoignez un événement existant !
            </Text>
          </View>
          <View className="gap-3 mt-4">
            <Button
              onPress={() => router.push("/create-event-with-template")}
              className="h-12 bg-primary"
            >
              <Text className="text-primary-foreground font-medium">
                🎯 Créer avec un template
              </Text>
            </Button>

            <View className="flex-row gap-3">
              <Button
                variant="outline"
                onPress={() => router.push("/join-event")}
                className="flex-1"
              >
                <Text className="text-foreground">🔗 Rejoindre</Text>
              </Button>
              <Button
                variant="outline"
                onPress={() => router.push("/event/create")}
                className="flex-1"
              >
                <Text className="text-foreground">✨ Créer libre</Text>
              </Button>
            </View>
          </View>
        </View>
      ) : (
        <FlatList
          data={events}
          renderItem={renderEventCard}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ padding: 16, paddingTop: 0 }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={refresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}
