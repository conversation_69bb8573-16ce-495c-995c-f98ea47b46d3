import React, { useState, useEffect, useMemo, useRef } from "react";
import {
  View,
  Text,
  FlatList,
  Pressable,
  TextInput,
  ActivityIndicator,
  ScrollView,
  Platform,
  Dimensions,
} from "react-native";
import { cn } from "~/lib/utils";
import emojiMartData from "@emoji-mart/data/sets/14/native.json";
// Icônes temporairement supprimées pour compatibilité web
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";

interface EmojiMartSkin {
  unified: string;
  native: string;
}
interface EmojiMartEntry {
  id: string;
  name: string;
  native?: string;
  keywords: string[];
  shortcodes?: string;
  emoticons?: string[];
  skins: EmojiMartSkin[];
  version?: number;
}

interface EmojiMartCategory {
  id: string;
  name: string;
  emojis: EmojiMartEntry["id"][];
}

export interface PickerEmoji {
  id: string;
  name: string;
  native: string;
  keywords: string[];
  baseId: string;
}

interface EmojiPickerProps {
  onEmojiSelected: (emoji: PickerEmoji) => void;
}

const transformEmojiData = (
  entry: EmojiMartEntry,
  skinIndex?: number
): PickerEmoji | null => {
  let nativeChar: string | undefined;
  let idSuffix = "";
  if (skinIndex !== undefined && entry.skins?.[skinIndex]) {
    nativeChar = entry.skins[skinIndex].native;
    if (skinIndex > 0) {
      idSuffix = `_skin_${skinIndex}`;
    }
  } else if (entry.skins?.[0]) {
    nativeChar = entry.skins[0].native;
  } else if (entry.native) {
    nativeChar = entry.native;
  }
  if (!nativeChar) return null;
  return {
    id: `${entry.id}${idSuffix}`,
    name: entry.name,
    native: nativeChar,
    keywords: entry.keywords || [],
    baseId: entry.id,
  };
};

const getCategoryIcon = (
  categoryId: string,
  categories: EmojiMartCategory[],
  emojiMap: { [id: string]: EmojiMartEntry }
): string | null => {
  if (categoryId === "all") return "All";
  const category = categories.find((cat) => cat.id === categoryId);
  if (category) {
    if (category.emojis.length > 0) {
      const firstEmojiBaseId = category.emojis[0];
      const emojiEntry = emojiMap[firstEmojiBaseId];
      if (emojiEntry?.skins?.[0]?.native) return emojiEntry.skins[0].native;
      if (emojiEntry?.native) return emojiEntry.native;
    }
    return category.name ? category.name.substring(0, 1).toUpperCase() : "??";
  }
  return "??";
};

const SKIN_TONE_COLORS = [
  "#FFDE5C", // Default Yellow
  "#FFE1BB", // Light
  "#FFD0A9", // Medium-Light
  "#D7A579", // Medium
  "#B57D52", // Medium-Dark
  "#8D552A", // Dark
];

export function EmojiPicker({ onEmojiSelected }: EmojiPickerProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("all");
  const [allEmojis, setAllEmojis] = useState<PickerEmoji[]>([]);
  const [categories, setCategories] = useState<EmojiMartCategory[]>([]);
  const [rawEmojiMap, setRawEmojiMap] = useState<{
    [id: string]: EmojiMartEntry;
  }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [currentCategoryName, setCurrentCategoryName] =
    useState("Tous les émojis");
  const [selectedSkinTone, setSelectedSkinTone] = useState<number>(0);
  const [isSkinTonePopoverOpen, setIsSkinTonePopoverOpen] = useState(false);
  const categoryScrollRef = useRef<ScrollView>(null);

  useEffect(() => {
    try {
      const loadedCategories = emojiMartData.categories as EmojiMartCategory[];
      const emojisFromData = emojiMartData.emojis as {
        [id: string]: EmojiMartEntry;
      };
      setRawEmojiMap(emojisFromData);
      const processedEmojisMap = new Map<string, PickerEmoji>();
      loadedCategories.forEach((category) => {
        category.emojis.forEach((emojiBaseId) => {
          const emojiEntry = emojisFromData[emojiBaseId];
          if (emojiEntry) {
            const baseEmoji = transformEmojiData(emojiEntry, 0);
            if (baseEmoji && !processedEmojisMap.has(baseEmoji.id)) {
              processedEmojisMap.set(baseEmoji.id, baseEmoji);
            }
            if (emojiEntry.skins && emojiEntry.skins.length > 1) {
              for (let i = 1; i < emojiEntry.skins.length; i++) {
                const skinEmoji = transformEmojiData(emojiEntry, i);
                if (skinEmoji && !processedEmojisMap.has(skinEmoji.id)) {
                  processedEmojisMap.set(skinEmoji.id, skinEmoji);
                }
              }
            }
          }
        });
      });
      const uniqueProcessedEmojis = Array.from(processedEmojisMap.values());
      setAllEmojis(uniqueProcessedEmojis);
      const allCategoryBaseIds = loadedCategories.reduce((acc, cat) => {
        cat.emojis.forEach((id) => acc.add(id));
        return acc;
      }, new Set<string>());
      setCategories([
        { id: "all", name: "All", emojis: Array.from(allCategoryBaseIds) },
        ...loadedCategories,
      ]);
      setIsLoading(false);
    } catch (error) {
      console.error("[EmojiPicker] Error loading emoji data:", error);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (selectedCategoryId === "all") {
      setCurrentCategoryName("Tous les émojis");
    } else {
      const currentCat = categories.find((c) => c.id === selectedCategoryId);
      if (currentCat) {
        setCurrentCategoryName(
          currentCat.name ||
            currentCat.id.charAt(0).toUpperCase() + currentCat.id.slice(1)
        );
      } else {
        setCurrentCategoryName("Emojis");
      }
    }
  }, [selectedCategoryId, categories]);

  const filteredEmojis = useMemo(() => {
    if (!allEmojis.length) return [];
    let emojisToDisplay = allEmojis;

    // Skin tone filtering
    if (selectedSkinTone > 0) {
      // A specific skin tone is selected
      emojisToDisplay = emojisToDisplay.filter((emoji) => {
        const rawEntry = rawEmojiMap[emoji.baseId];
        if (rawEntry && rawEntry.skins && rawEntry.skins.length > 1) {
          // It's an emoji with skin tones, keep only the selected skin tone
          return emoji.id === `${emoji.baseId}_skin_${selectedSkinTone}`;
        }
        // It's an emoji without skin tones, keep it
        return true;
      });
    } else {
      // Default (yellow) skin tone is selected (selectedSkinTone === 0)
      emojisToDisplay = emojisToDisplay.filter((emoji) => {
        const rawEntry = rawEmojiMap[emoji.baseId];
        if (rawEntry && rawEntry.skins && rawEntry.skins.length > 1) {
          // It's an emoji with skin tones, keep only the base (yellow) version
          return !emoji.id.includes("_skin_");
        }
        // It's an emoji without skin tones, keep it
        return true;
      });
    }

    if (selectedCategoryId && selectedCategoryId !== "all") {
      const currentCategory = categories.find(
        (cat) => cat.id === selectedCategoryId
      );
      if (currentCategory) {
        const categoryEmojiBaseIds = new Set(currentCategory.emojis);
        emojisToDisplay = emojisToDisplay.filter((emoji) =>
          categoryEmojiBaseIds.has(emoji.baseId)
        );
      }
    }

    if (searchQuery) {
      const lowerSearchQuery = searchQuery.toLowerCase();
      emojisToDisplay = emojisToDisplay.filter(
        (e) =>
          e.name.toLowerCase().includes(lowerSearchQuery) ||
          e.keywords.some((k) => k.toLowerCase().includes(lowerSearchQuery))
      );
    }
    return emojisToDisplay;
  }, [
    searchQuery,
    selectedCategoryId,
    allEmojis,
    categories,
    selectedSkinTone,
  ]);

  const handleRandomEmoji = () => {
    if (allEmojis.length > 0) {
      const randomIndex = Math.floor(Math.random() * allEmojis.length);
      onEmojiSelected(allEmojis[randomIndex]);
    }
  };

  const handleSkinToneSelect = (
    toneIndex: number,
    event?: import("react-native").GestureResponderEvent // Optional event for stopPropagation
  ) => {
    if (event) {
      event.stopPropagation();
    }
    setSelectedSkinTone(toneIndex);
    setIsSkinTonePopoverOpen(false); // Close popover after selection
  };

  // Gestion des gestes de swipe pour la navigation entre catégories avec événements tactiles natifs
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(
    null
  );
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(
    null
  );

  const handleTouchStart = (event: any) => {
    const touch = event.nativeEvent.touches[0];
    setTouchStart({ x: touch.pageX, y: touch.pageY });
    setTouchEnd(null);
  };

  const handleTouchMove = (event: any) => {
    const touch = event.nativeEvent.touches[0];
    setTouchEnd({ x: touch.pageX, y: touch.pageY });
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const deltaX = touchStart.x - touchEnd.x;
    const deltaY = touchStart.y - touchEnd.y;
    const minSwipeDistance = 50;

    // Vérifier que c'est un swipe horizontal (plus horizontal que vertical)
    if (
      Math.abs(deltaX) > Math.abs(deltaY) &&
      Math.abs(deltaX) > minSwipeDistance
    ) {
      const currentIndex = categories.findIndex(
        (cat) => cat.id === selectedCategoryId
      );

      if (deltaX > 0 && currentIndex < categories.length - 1) {
        // Swipe vers la gauche - catégorie suivante
        setSelectedCategoryId(categories[currentIndex + 1].id);
        scrollToCategoryInView(currentIndex + 1);
      } else if (deltaX < 0 && currentIndex > 0) {
        // Swipe vers la droite - catégorie précédente
        setSelectedCategoryId(categories[currentIndex - 1].id);
        scrollToCategoryInView(currentIndex - 1);
      }
    }

    setTouchStart(null);
    setTouchEnd(null);
  };

  // Scroll to category function for horizontal navigation
  const scrollToCategoryInView = (categoryIndex: number) => {
    if (categoryScrollRef.current) {
      const scrollPosition = categoryIndex * 56; // 48px width + 8px margin
      categoryScrollRef.current.scrollTo({ x: scrollPosition, animated: true });
    }
  };

  const renderEmojiCell = ({ item }: { item: PickerEmoji }) => {
    if (Platform.OS === "web") {
      // Version simplifiée pour le web sans Tooltip - emojis plus grands
      return (
        <Pressable
          onPress={() => onEmojiSelected(item)}
          className="p-1 items-center justify-center w-full h-full rounded hover:bg-gray-100 dark:hover:bg-gray-800"
          title={item.name} // Utilise le title HTML natif pour le tooltip
        >
          <Text className="text-4xl">{item.native}</Text>
        </Pressable>
      );
    }

    // Version simplifiée pour mobile sans Tooltip - emojis plus grands
    return (
      <Pressable
        onPress={() => onEmojiSelected(item)}
        className="p-1 items-center justify-center w-full h-full rounded hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <Text className="text-4xl">{item.native}</Text>
      </Pressable>
    );
  };

  const renderGridItem = ({ item }: { item: PickerEmoji }) => (
    <View className="w-[14.28%] aspect-square">
      {renderEmojiCell({ item })}
    </View>
  );

  if (isLoading) {
    return (
      <View className="bg-popover p-4 rounded-lg shadow-xl border border-border w-full items-center justify-center h-[400px]">
        <ActivityIndicator size="large" />
        <Text className="mt-2 text-muted-foreground">Loading Emojis...</Text>
      </View>
    );
  }

  return (
    <Pressable
      onPress={() => setIsSkinTonePopoverOpen(false)}
      className="bg-popover p-3 rounded-lg shadow-xl border border-border w-full max-w-[360px] max-h-[500px] h-[500px] flex flex-col"
    >
      {/* Responsive width: w-full on mobile, max-w-[360px] on larger screens */}
      {/* Tab View Removed */}
      <View className="flex-row items-center h-11 mb-2.5 mt-2 relative">
        {/* Barre de recherche plus petite */}
        <Pressable
          onPress={(e) => e.stopPropagation()}
          className="flex-1 max-w-[200px] flex-row items-center h-full px-2.5 rounded-md border border-input bg-background focus-within:border-primary focus-within:outline-none web:focus-within:ring-2 web:focus-within:ring-ring web:focus-within:ring-offset-2 hover:bg-muted/50 mr-3"
        >
          <Text className="text-muted-foreground mr-2 text-sm">🔍</Text>
          <TextInput
            placeholder="Filtrer..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            className="flex-1 h-full text-foreground text-sm py-0 focus:outline-none web:focus:outline-none"
            placeholderTextColor="hsl(var(--muted-foreground))"
            textAlignVertical="center"
          />
          {searchQuery.length > 0 && (
            <Pressable
              onPress={() => setSearchQuery("")}
              className="p-1 items-center justify-center"
            >
              <Text className="text-muted-foreground text-sm">❌</Text>
            </Pressable>
          )}
        </Pressable>
        {/* Boutons plus grands */}
        {Platform.OS === "web" ? (
          <Pressable
            onPress={(e) => {
              e.stopPropagation();
              handleRandomEmoji();
            }}
            className="p-3 ml-2 items-center justify-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 h-11 w-11"
            title="Emoji Aléatoire"
          >
            <Text className="text-muted-foreground text-xl">🎲</Text>
          </Pressable>
        ) : (
          <Pressable
            onPress={(e) => {
              e.stopPropagation();
              handleRandomEmoji();
            }}
            className="p-3 ml-2 items-center justify-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 h-11 w-11"
            aria-label="Choisir un emoji aléatoire"
          >
            <Text className="text-muted-foreground text-xl">🎲</Text>
          </Pressable>
        )}
        <Pressable
          className="p-3 ml-2 items-center justify-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 h-11 w-11"
          onPress={(e) => {
            e.stopPropagation();
            setIsSkinTonePopoverOpen(!isSkinTonePopoverOpen);
          }}
          {...(Platform.OS === "web"
            ? { title: "Couleur de Peau" }
            : { "aria-label": "Choisir la couleur de peau" })}
        >
          <Text className="text-muted-foreground text-xl">✋</Text>
        </Pressable>
      </View>
      {/* Grille d'emojis avec gestion des swipes */}
      <View
        className="flex-1"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <FlatList
          data={filteredEmojis}
          renderItem={renderGridItem}
          keyExtractor={(item) => item.id}
          numColumns={7}
          showsVerticalScrollIndicator={true}
          contentContainerClassName="pb-1"
        />
      </View>

      {/* Navigation par catégories horizontale - déplacée en bas */}
      <Pressable
        onPress={(e) => e.stopPropagation()}
        className="mt-3 border-t border-border pt-2"
      >
        <ScrollView
          ref={categoryScrollRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 4 }}
          className="flex-row"
        >
          {categories.map((cat) => {
            const categoryIcon = getCategoryIcon(
              cat.id,
              categories,
              rawEmojiMap
            );
            return (
              <Pressable
                key={cat.id}
                onPress={() => {
                  setSelectedCategoryId(cat.id);
                  const categoryIndex = categories.findIndex(
                    (c) => c.id === cat.id
                  );
                  scrollToCategoryInView(categoryIndex);
                }}
                className={cn(
                  "p-3 rounded-lg items-center justify-center h-12 w-12 min-w-[48px] mx-1",
                  selectedCategoryId === cat.id
                    ? "bg-accent"
                    : "hover:bg-muted/50"
                )}
                aria-label={`Category ${cat.name}`}
              >
                {categoryIcon === "All" ? (
                  <Text
                    className={cn(
                      "text-xl",
                      selectedCategoryId === cat.id
                        ? "text-accent-foreground"
                        : "text-muted-foreground opacity-70"
                    )}
                  >
                    😊
                  </Text>
                ) : (
                  <Text
                    className={cn(
                      "text-xl",
                      selectedCategoryId === cat.id
                        ? "text-accent-foreground"
                        : "text-muted-foreground opacity-70"
                    )}
                  >
                    {categoryIcon || "?"}
                  </Text>
                )}
              </Pressable>
            );
          })}
        </ScrollView>
      </Pressable>

      {/* Unified skin tone selector for all platforms */}
      {isSkinTonePopoverOpen && (
        <View
          className="absolute bg-popover border border-border rounded-md shadow-lg p-1"
          style={{
            top: 60, // Position it below the search/controls area
            right: 12, // Align with the right edge
            zIndex: 9999,
            elevation: 1000,
          }}
        >
          <View className="flex-row">
            {SKIN_TONE_COLORS.map((color, index) => (
              <Pressable
                key={color}
                onPress={() => handleSkinToneSelect(index)}
                className="p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <View
                  style={{
                    backgroundColor: color,
                    width: 24,
                    height: 24,
                    borderRadius: 12,
                    margin: 2,
                    borderWidth: selectedSkinTone === index ? 2 : 0,
                    borderColor: "hsl(var(--primary))",
                  }}
                />
              </Pressable>
            ))}
          </View>
        </View>
      )}
    </Pressable>
  );
}
