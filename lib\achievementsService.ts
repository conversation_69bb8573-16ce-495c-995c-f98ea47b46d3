/**
 * Service de gestion des achievements pour Party Organizer
 * Gère les badges, progression et gamification
 */

import { supabase } from './supabase';
import { cache, CACHE_KEYS } from './cacheService';
import { showToast } from './toastService';

export interface Badge {
  id: number;
  name: string;
  title: string;
  description: string;
  icon: string;
  category: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  requirements: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserAchievement {
  id: number;
  user_id: string;
  badge_id: number;
  unlocked_at: string;
  progress: Record<string, any>;
  is_completed: boolean;
  completion_data: Record<string, any>;
  badge?: Badge;
}

export interface UserStats {
  id: number;
  user_id: string;
  total_points: number;
  level: number;
  events_organized: number;
  events_participated: number;
  total_participants_invited: number;
  total_items_managed: number;
  total_money_saved: number;
  perfect_events: number;
  streak_days: number;
  last_activity_date: string;
  achievements_unlocked: number;
  created_at: string;
  updated_at: string;
}

export interface AchievementProgress {
  badge: Badge;
  current: number;
  required: number;
  percentage: number;
  is_completed: boolean;
  unlocked_at?: string;
}

export interface LeaderboardEntry {
  user_id: string;
  username?: string;
  total_points: number;
  level: number;
  achievements_unlocked: number;
  rank: number;
}

class AchievementsService {
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Récupère tous les badges disponibles
   */
  async getBadges(): Promise<Badge[]> {
    try {
      const cacheKey = 'all_badges';
      const cached = await cache.get<Badge[]>(cacheKey);
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('badges')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true })
        .order('points', { ascending: true });

      if (error) {
        throw error;
      }

      const badges = data || [];
      await cache.set(cacheKey, badges, {}, this.CACHE_TTL);
      
      return badges;
    } catch (error) {
      console.error('Error fetching badges:', error);
      return [];
    }
  }

  /**
   * Récupère les achievements d'un utilisateur
   */
  async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    try {
      const cacheKey = 'user_achievements';
      const cached = await cache.get<UserAchievement[]>(cacheKey, { userId });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('user_achievements')
        .select(`
          *,
          badge:badges(*)
        `)
        .eq('user_id', userId)
        .order('unlocked_at', { ascending: false });

      if (error) {
        throw error;
      }

      const achievements = data || [];
      await cache.set(cacheKey, achievements, { userId }, this.CACHE_TTL);
      
      return achievements;
    } catch (error) {
      console.error('Error fetching user achievements:', error);
      return [];
    }
  }

  /**
   * Récupère les statistiques d'un utilisateur
   */
  async getUserStats(userId: string): Promise<UserStats | null> {
    try {
      const cacheKey = 'user_stats';
      const cached = await cache.get<UserStats>(cacheKey, { userId });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('user_stats')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // Pas d'erreur si pas trouvé
        throw error;
      }

      const stats = data || null;
      if (stats) {
        await cache.set(cacheKey, stats, { userId }, this.CACHE_TTL);
      }
      
      return stats;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return null;
    }
  }

  /**
   * Met à jour les statistiques utilisateur
   */
  async updateUserStats(
    userId: string,
    updates: {
      events_organized?: number;
      events_participated?: number;
      participants_invited?: number;
      items_managed?: number;
      money_saved?: number;
      perfect_events?: number;
    }
  ): Promise<void> {
    try {
      const { error } = await supabase.rpc('update_user_stats', {
        p_user_id: userId,
        p_events_organized: updates.events_organized || 0,
        p_events_participated: updates.events_participated || 0,
        p_participants_invited: updates.participants_invited || 0,
        p_items_managed: updates.items_managed || 0,
        p_money_saved: updates.money_saved || 0,
        p_perfect_events: updates.perfect_events || 0,
      });

      if (error) {
        throw error;
      }

      // Invalider le cache
      await cache.delete('user_stats', { userId });
      
      // Vérifier les nouveaux achievements
      await this.checkAndUnlockAchievements(userId);
    } catch (error) {
      console.error('Error updating user stats:', error);
      throw error;
    }
  }

  /**
   * Vérifie et débloque les achievements
   */
  async checkAndUnlockAchievements(userId: string): Promise<Badge[]> {
    try {
      const { data, error } = await supabase.rpc('check_and_unlock_achievements', {
        p_user_id: userId,
      });

      if (error) {
        throw error;
      }

      const newBadges = data || [];
      
      // Invalider les caches
      await cache.delete('user_achievements', { userId });
      await cache.delete('user_stats', { userId });

      // Notifier les nouveaux badges
      for (const badge of newBadges) {
        this.celebrateNewAchievement(badge);
      }

      return newBadges;
    } catch (error) {
      console.error('Error checking achievements:', error);
      return [];
    }
  }

  /**
   * Célèbre un nouvel achievement
   */
  private celebrateNewAchievement(badge: any): void {
    const rarityEmojis = {
      common: '🎉',
      rare: '✨',
      epic: '🌟',
      legendary: '👑',
    };

    const emoji = rarityEmojis[badge.rarity as keyof typeof rarityEmojis] || '🎉';
    
    showToast(
      `${emoji} Nouveau badge débloqué : ${badge.badge_title}!`,
      { 
        type: 'success',
        duration: 5000,
      }
    );
  }

  /**
   * Calcule la progression vers les badges
   */
  async getAchievementProgress(userId: string): Promise<AchievementProgress[]> {
    try {
      const [badges, userStats, userAchievements] = await Promise.all([
        this.getBadges(),
        this.getUserStats(userId),
        this.getUserAchievements(userId),
      ]);

      if (!userStats) {
        return [];
      }

      const completedBadgeIds = new Set(
        userAchievements
          .filter(a => a.is_completed)
          .map(a => a.badge_id)
      );

      const progress: AchievementProgress[] = [];

      for (const badge of badges) {
        const isCompleted = completedBadgeIds.has(badge.id);
        const unlockedAchievement = userAchievements.find(a => a.badge_id === badge.id);

        let current = 0;
        let required = 1;

        // Calculer la progression selon les requirements
        const requirements = badge.requirements;
        
        if (requirements.events_organized) {
          current = userStats.events_organized;
          required = requirements.events_organized;
        } else if (requirements.events_participated) {
          current = userStats.events_participated;
          required = requirements.events_participated;
        } else if (requirements.total_points) {
          current = userStats.total_points;
          required = requirements.total_points;
        } else if (requirements.perfect_events) {
          current = userStats.perfect_events;
          required = requirements.perfect_events;
        } else if (requirements.level) {
          current = userStats.level;
          required = requirements.level;
        } else if (requirements.total_participants_invited) {
          current = userStats.total_participants_invited;
          required = requirements.total_participants_invited;
        } else if (requirements.total_money_saved) {
          current = userStats.total_money_saved;
          required = requirements.total_money_saved;
        }

        const percentage = Math.min(100, (current / required) * 100);

        progress.push({
          badge,
          current,
          required,
          percentage,
          is_completed: isCompleted,
          unlocked_at: unlockedAchievement?.unlocked_at,
        });
      }

      // Trier par progression décroissante, puis par rareté
      progress.sort((a, b) => {
        if (a.is_completed !== b.is_completed) {
          return a.is_completed ? 1 : -1; // Complétés à la fin
        }
        if (a.percentage !== b.percentage) {
          return b.percentage - a.percentage; // Plus proche en premier
        }
        const rarityOrder = { common: 1, rare: 2, epic: 3, legendary: 4 };
        return rarityOrder[a.badge.rarity] - rarityOrder[b.badge.rarity];
      });

      return progress;
    } catch (error) {
      console.error('Error calculating achievement progress:', error);
      return [];
    }
  }

  /**
   * Récupère le classement des utilisateurs
   */
  async getLeaderboard(limit: number = 50): Promise<LeaderboardEntry[]> {
    try {
      const cacheKey = 'leaderboard';
      const cached = await cache.get<LeaderboardEntry[]>(cacheKey, { limit });
      
      if (cached) {
        return cached;
      }

      const { data, error } = await supabase
        .from('user_stats')
        .select(`
          user_id,
          total_points,
          level,
          achievements_unlocked
        `)
        .order('total_points', { ascending: false })
        .order('level', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      const leaderboard = (data || []).map((entry, index) => ({
        ...entry,
        rank: index + 1,
      }));

      await cache.set(cacheKey, leaderboard, { limit }, this.CACHE_TTL);
      
      return leaderboard;
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      return [];
    }
  }

  /**
   * Récupère les badges par catégorie
   */
  async getBadgesByCategory(): Promise<Record<string, Badge[]>> {
    try {
      const badges = await this.getBadges();
      
      const categories: Record<string, Badge[]> = {};
      
      for (const badge of badges) {
        if (!categories[badge.category]) {
          categories[badge.category] = [];
        }
        categories[badge.category].push(badge);
      }

      return categories;
    } catch (error) {
      console.error('Error grouping badges by category:', error);
      return {};
    }
  }

  /**
   * Calcule le niveau suivant et les points requis
   */
  calculateNextLevel(currentPoints: number): { currentLevel: number; nextLevel: number; pointsRequired: number; progress: number } {
    const currentLevel = Math.floor(Math.sqrt(currentPoints / 100)) + 1;
    const nextLevel = currentLevel + 1;
    const pointsForNextLevel = Math.pow(nextLevel - 1, 2) * 100;
    const pointsRequired = pointsForNextLevel - currentPoints;
    const pointsForCurrentLevel = Math.pow(currentLevel - 1, 2) * 100;
    const progress = ((currentPoints - pointsForCurrentLevel) / (pointsForNextLevel - pointsForCurrentLevel)) * 100;

    return {
      currentLevel,
      nextLevel,
      pointsRequired: Math.max(0, pointsRequired),
      progress: Math.min(100, Math.max(0, progress)),
    };
  }

  /**
   * Invalide tous les caches d'achievements
   */
  async invalidateCache(userId?: string): Promise<void> {
    await cache.invalidate('all_badges');
    await cache.invalidate('leaderboard');
    
    if (userId) {
      await cache.delete('user_achievements', { userId });
      await cache.delete('user_stats', { userId });
    }
  }
}

// Instance singleton
export const achievementsService = new AchievementsService();

// Fonctions utilitaires
export const achievements = {
  getBadges: () => achievementsService.getBadges(),
  getUserAchievements: (userId: string) => achievementsService.getUserAchievements(userId),
  getUserStats: (userId: string) => achievementsService.getUserStats(userId),
  updateStats: (userId: string, updates: any) => achievementsService.updateUserStats(userId, updates),
  checkAchievements: (userId: string) => achievementsService.checkAndUnlockAchievements(userId),
  getProgress: (userId: string) => achievementsService.getAchievementProgress(userId),
  getLeaderboard: (limit?: number) => achievementsService.getLeaderboard(limit),
  getBadgesByCategory: () => achievementsService.getBadgesByCategory(),
  calculateNextLevel: (points: number) => achievementsService.calculateNextLevel(points),
  invalidateCache: (userId?: string) => achievementsService.invalidateCache(userId),
};
