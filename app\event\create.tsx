import React, { useState, useEffect } from "react";
import { View, ScrollView, Alert, Platform, Modal } from "react-native";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { DatePicker } from "~/components/ui/date-picker";
import { TimePicker } from "~/components/ui/time-picker";

import { Dialog, DialogContent } from "~/components/ui/dialog"; // Import Dialog components
import { EmojiPicker, PickerEmoji } from "~/components/EmojiPicker"; // Import EmojiPicker
// Icône temporairement supprimée pour compatibilité web
import { cn } from "~/lib/utils"; // Import cn utility

import { useAuth } from "~/lib/AuthContext";
import { createEvent, createParticipant } from "~/lib/supabaseCrud";

import { useRouter } from "expo-router";
import { showToast } from "~/lib/toastService";
import { EventOptionsSection } from "~/components/EventOptionsSection";
import { ParticipantManager } from "~/components/ParticipantManager";
// Assuming ParticipantRole and ParticipantStatus are now string literal unions from database.types.ts
// If they are still enums from lib/types.ts, adjust import if needed.
// For now, assuming they are string literals as per recent refactoring.
// import { ParticipantRole, ParticipantStatus } from "~/lib/types";
type ParticipantRoleEnum = Database["public"]["Enums"]["participant_role"];
type ParticipantStatusEnum = Database["public"]["Enums"]["participant_status"];
import { Database } from "~/lib/database.types";

// Interface pour les erreurs de validation
interface ValidationErrors {
  title?: string;
  dateTime?: string;
}

export default function CreateEventScreen() {
  const { session } = useAuth();
  const router = useRouter();

  // États du formulaire
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [dateTime, setDateTime] = useState(() => {
    const defaultDate = new Date();
    defaultDate.setHours(19, 0, 0, 0); // 19h00 par défaut
    return defaultDate;
  });
  const [location, setLocation] = useState("");
  const [icon, setIcon] = useState(""); // Will store the native emoji string
  const [allowSuggestions, setAllowSuggestions] = useState(false);
  const [allowPreAssignment, setAllowPreAssignment] = useState(false);

  // États pour les participants
  const [participantNames, setParticipantNames] = useState<string[]>([]);

  // États UI
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [creationError, setCreationError] = useState<string | null>(null);
  const [showEmojiPickerDialog, setShowEmojiPickerDialog] = useState(false);

  useEffect(() => {
    if (submitAttempted) {
      validateForm();
      // S'assurer que loading est remis à false si il y a des erreurs de validation
      if (loading) {
        const hasErrors = !validateForm();
        if (hasErrors) {
          setLoading(false);
        }
      }
    }
  }, [title, dateTime, submitAttempted, loading]);

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    if (!title.trim()) {
      newErrors.title = "Le titre est obligatoire";
    }
    const now = new Date();
    now.setSeconds(0, 0);
    const dateToCheck = new Date(dateTime);
    dateToCheck.setSeconds(0, 0);
    const isSameDay =
      dateToCheck.getDate() === now.getDate() &&
      dateToCheck.getMonth() === now.getMonth() &&
      dateToCheck.getFullYear() === now.getFullYear();
    if (isSameDay) {
      const nowTime = now.getHours() * 60 + now.getMinutes() - 5;
      const eventTime = dateToCheck.getHours() * 60 + dateToCheck.getMinutes();
      if (eventTime <= nowTime) {
        newErrors.dateTime =
          "Pour aujourd'hui, l'heure doit être dans le futur";
      }
    } else if (dateToCheck < now) {
      newErrors.dateTime = "La date ne peut pas être dans le passé";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleIconSelect = (selectedEmoji: PickerEmoji) => {
    setIcon(selectedEmoji.native);
    setShowEmojiPickerDialog(false); // Close the dialog
  };

  const handleCreateEvent = async () => {
    setSubmitAttempted(true);
    setCreationError(null);
    if (!session?.user?.id) {
      showToast("Vous devez être connecté pour créer un événement.", {
        type: "error",
      });
      return;
    }
    if (!validateForm()) {
      Alert.alert(
        "Formulaire incomplet",
        "Veuillez corriger les erreurs dans le formulaire."
      );
      return;
    }

    try {
      setLoading(true);
      const eventData = {
        title,
        description: description || null,
        date_time: dateTime.toISOString(),
        location: location || null,
        icon: icon || null,
        allow_suggestions: allowSuggestions,
        allow_pre_assignment: allowPreAssignment,
        organizer_delegated: false,
      };

      const createdEvent = await createEvent(eventData, session.user.id);

      if (!createdEvent) {
        setCreationError(
          "Impossible de créer l'événement. Veuillez réessayer."
        );
        showToast("Erreur lors de la création de l'événement.", {
          type: "error",
        });
        setLoading(false);
        return;
      }

      const participantData = {
        event_id: createdEvent.id,
        user_id: session.user.id,
        role: "organizer" as ParticipantRoleEnum, // Use string literal
        status: "accepted" as ParticipantStatusEnum, // Use string literal
        anonymous_name: null,
        anonymous_email: null,
        anonymous_phone: null,
        invitation_token: null,
      };

      const participantResult = await createParticipant(participantData);

      if (participantResult) {
        // Créer les participants ajoutés
        if (participantNames.length > 0) {
          console.log("Création des participants:", participantNames);

          for (const name of participantNames) {
            try {
              // Générer un token unique pour chaque participant
              const invitationToken = crypto.randomUUID();

              // Créer un participant avec nom anonyme
              const participantData = {
                event_id: createdEvent.id,
                user_id: null, // Participant anonyme pour l'instant
                role: "guest" as ParticipantRoleEnum,
                status: "pending" as ParticipantStatusEnum,
                anonymous_name: name,
                anonymous_email: null,
                anonymous_phone: null,
                invitation_token: invitationToken,
              };

              await createParticipant(participantData);
              console.log(`Participant créé pour ${name}`);
            } catch (error) {
              console.error(
                `Erreur lors de la création du participant ${name}:`,
                error
              );
            }
          }

          showToast(`${participantNames.length} participants ajoutés !`, {
            type: "success",
          });
        }

        showToast(
          "🎉 Événement créé avec succès ! Vous pouvez maintenant le partager depuis la page de détail.",
          { type: "success" }
        );

        // Reset du formulaire
        setTitle("");
        setDescription("");
        const resetDate = new Date();
        resetDate.setHours(19, 0, 0, 0); // 19h00 par défaut
        setDateTime(resetDate);
        setLocation("");
        setIcon("");
        setAllowSuggestions(false);
        setAllowPreAssignment(false);
        setParticipantNames([]);
        setSubmitAttempted(false);

        // Redirection vers l'accueil avec un délai pour voir le toast
        setTimeout(() => {
          router.replace("/(tabs)");
        }, 1500);
      } else {
        setCreationError(
          "L'événement a été créé mais vous n'avez pas été ajouté comme organisateur."
        );
        showToast(
          "Événement créé, mais erreur lors de l'ajout de l'organisateur.",
          { type: "error" }
        );
        setLoading(false);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erreur inconnue";
      setCreationError(`Une erreur est survenue: ${errorMessage}`);
      showToast(`Erreur inattendue: ${errorMessage}`, { type: "error" });
    } finally {
      setLoading(false);
    }
  };

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <Text className="text-sm text-destructive mt-1">{message}</Text>;
  };

  return (
    <ScrollView
      className="flex-1 bg-background"
      contentContainerStyle={{ paddingVertical: 24, paddingHorizontal: 16 }}
    >
      <View className={Platform.OS === "web" ? "max-w-2xl mx-auto" : "w-full"}>
        <View className="mb-8">
          <Text className="text-2xl font-bold text-center mb-2 text-foreground">
            Nouvel Événement
          </Text>
          <Text className="text-muted-foreground text-center">
            Créez votre événement en quelques étapes
          </Text>
        </View>

        {creationError && (
          <View className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg mb-6">
            <Text className="text-destructive">{creationError}</Text>
          </View>
        )}

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Informations principales
          </Text>
          <View className="mb-5">
            <Label nativeID="titleLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Titre</Text>
            </Label>
            <Input
              nativeID="titleLabel"
              placeholder="Ex: Anniversaire de Gauthier"
              value={title}
              onChangeText={setTitle}
              aria-required="true"
              className={`h-11 ${
                errors.title ? "border-destructive" : "border-border"
              }`}
            />
            <ErrorMessage message={errors.title} />
          </View>
          <View className="mb-5">
            <Label
              nativeID="descLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Description
            </Label>
            <Textarea
              nativeID="descLabel"
              placeholder="Détails supplémentaires (thème, code vestimentaire...)"
              value={description}
              onChangeText={setDescription}
              numberOfLines={3}
              className="border-border"
            />
          </View>
          <View className="mb-2">
            <Label nativeID="dateTimeLabel" className="flex-row mb-1.5">
              <Text className="text-destructive mr-1">*</Text>
              <Text className="font-medium text-foreground">Date et Heure</Text>
            </Label>
            <View
              className={Platform.OS === "web" ? "flex-row gap-4" : "gap-4"}
            >
              <View className={Platform.OS === "web" ? "flex-1" : "mb-4"}>
                <Label
                  nativeID="dateLabel"
                  className="mb-1.5 font-medium text-foreground"
                >
                  Date
                </Label>
                <DatePicker
                  value={dateTime}
                  onChange={setDateTime}
                  minimumDate={new Date()}
                  error={!!errors.dateTime}
                  placeholder="Sélectionner une date"
                />
              </View>
              <View className={Platform.OS === "web" ? "flex-1" : ""}>
                <Label
                  nativeID="timeLabel"
                  className="mb-1.5 font-medium text-foreground"
                >
                  Heure
                </Label>
                <TimePicker
                  value={dateTime}
                  onChange={setDateTime}
                  error={!!errors.dateTime}
                  placeholder="Sélectionner une heure"
                />
              </View>
            </View>
            <ErrorMessage message={errors.dateTime} />
          </View>
        </View>

        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Détails supplémentaires
          </Text>
          <View className="mb-5">
            <Label
              nativeID="locationLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Lieu
            </Label>
            <Input
              nativeID="locationLabel"
              placeholder="Ex: 12 Rue de la Paix, Paris"
              value={location}
              onChangeText={setLocation}
              className="h-11 border-border"
            />
          </View>

          {/* Icône (Emoji Picker) */}
          <View className="mb-5">
            <Label
              nativeID="iconLabel"
              className="mb-1.5 font-medium text-foreground"
            >
              Icône
            </Label>
            <Button
              variant="outline"
              className="h-12 w-full flex-row items-center justify-between px-3 py-2"
              onPress={() => setShowEmojiPickerDialog(true)}
            >
              <Text
                className={cn(
                  "text-sm",
                  icon ? "text-foreground" : "text-muted-foreground"
                )}
              >
                {icon || "Choisir une icône"}
              </Text>
              {icon ? (
                <Text className="text-2xl">{icon}</Text>
              ) : (
                <Text className="text-muted-foreground text-xl">😊</Text>
              )}
            </Button>
            {Platform.OS === "web" ? (
              <Dialog
                open={showEmojiPickerDialog}
                onOpenChange={setShowEmojiPickerDialog}
              >
                <DialogContent className="p-0 w-auto max-w-md bg-transparent border-none shadow-none z-[9999] web:z-[999999999]">
                  <EmojiPicker onEmojiSelected={handleIconSelect} />
                </DialogContent>
              </Dialog>
            ) : (
              <Modal
                visible={showEmojiPickerDialog}
                transparent={true}
                animationType="slide"
                onRequestClose={() => setShowEmojiPickerDialog(false)}
                statusBarTranslucent={true}
              >
                <View className="flex-1 justify-center items-center bg-black/50 p-4">
                  <View className="bg-background rounded-lg w-full max-w-md max-h-[85%]">
                    <View className="flex-row justify-between items-center p-4 border-b border-border">
                      <Text className="text-lg font-semibold">
                        Choisir une icône
                      </Text>
                      <Button
                        variant="ghost"
                        onPress={() => setShowEmojiPickerDialog(false)}
                      >
                        <Text>✕</Text>
                      </Button>
                    </View>
                    <EmojiPicker onEmojiSelected={handleIconSelect} />
                  </View>
                </View>
              </Modal>
            )}
          </View>
        </View>

        <EventOptionsSection
          allowSuggestions={allowSuggestions}
          allowPreAssignment={allowPreAssignment}
          onAllowSuggestionsChange={setAllowSuggestions}
          onAllowPreAssignmentChange={setAllowPreAssignment}
        />

        {/* Section Participants */}
        <View
          className={`bg-card border border-border rounded-xl shadow-sm mb-6 ${
            Platform.OS === "web" ? "p-6" : "p-4"
          }`}
        >
          <Text className="text-lg font-semibold mb-4 text-foreground">
            Participants
          </Text>
          <Text className="text-sm text-muted-foreground mb-4">
            Ajoutez les noms des personnes que vous souhaitez inviter
            (optionnel)
          </Text>

          <ParticipantManager
            participants={participantNames}
            onParticipantsChange={setParticipantNames}
            currentUserName="Vous"
            placeholder="Ex: Maxence Manson"
            addButtonText="Ajouter"
          />
        </View>

        {/* Section Prévisualisation */}
        {title && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-lg">Prévisualisation</CardTitle>
            </CardHeader>
            <CardContent>
              <View className="flex-row items-center mb-3">
                <Text className="text-2xl mr-3">{icon || "🎉"}</Text>
                <View className="flex-1">
                  <Text className="text-lg font-semibold text-foreground">
                    {title}
                  </Text>
                  <Text className="text-sm text-muted-foreground">
                    {dateTime.toLocaleDateString("fr-FR", {
                      weekday: "long",
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}{" "}
                    à{" "}
                    {dateTime.toLocaleTimeString("fr-FR", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </Text>
                </View>
              </View>
              {location && (
                <Text className="text-sm text-muted-foreground mb-2">
                  📍 {location}
                </Text>
              )}
              {description && (
                <Text className="text-sm text-foreground mb-2">
                  {description}
                </Text>
              )}
              {participantNames.length > 0 && (
                <Text className="text-sm text-muted-foreground">
                  👥 {participantNames.length + 1} participant
                  {participantNames.length > 0 ? "s" : ""} (vous inclus)
                </Text>
              )}
            </CardContent>
          </Card>
        )}

        <Button
          onPress={handleCreateEvent}
          disabled={loading}
          loading={loading}
          className="h-12 bg-primary rounded-lg mb-4"
        >
          <Text className="text-primary-foreground font-medium text-center w-full">
            {loading ? "Création en cours..." : "Créer l'Événement"}
          </Text>
        </Button>
        <Text className="text-sm text-muted-foreground text-center mb-8">
          <Text className="text-destructive">*</Text> Champs obligatoires
        </Text>
      </View>
    </ScrollView>
  );
}
