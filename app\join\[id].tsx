import React, { useState, useEffect } from "react";
import { View, ScrollView, ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Text } from "~/components/ui/text";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  fetchEventDetails,
  fetchParticipantsForEvent,
  updateParticipant,
  createParticipant,
} from "~/lib/supabaseCrud";
import { useAuth } from "~/lib/AuthContext";
import { showToast } from "~/lib/toastService";
import { Event, Participant } from "~/lib/types";

export default function JoinEventScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { session } = useAuth();
  const [event, setEvent] = useState<Event | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [selectedParticipant, setSelectedParticipant] =
    useState<Participant | null>(null);
  const [customName, setCustomName] = useState("");
  const [showCustomNameInput, setShowCustomNameInput] = useState(false);

  useEffect(() => {
    if (id) {
      loadEventData();
    }
  }, [id]);

  const loadEventData = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const eventId = parseInt(id);

      const [eventData, participantsData] = await Promise.all([
        fetchEventDetails(eventId),
        fetchParticipantsForEvent(eventId),
      ]);

      if (eventData) {
        setEvent(eventData);
        // Filtrer pour ne montrer que les participants avec des noms (pas l'organisateur)
        const availableParticipants = participantsData.filter(
          (p) =>
            p.anonymous_name && p.status === "pending" && p.role === "guest"
        );
        setParticipants(availableParticipants);
      } else {
        showToast("Événement non trouvé", { type: "error" });
        router.push("/(tabs)");
      }
    } catch (error) {
      console.error("Error loading event:", error);
      showToast("Erreur lors du chargement de l'événement", { type: "error" });
      router.push("/(tabs)");
    } finally {
      setLoading(false);
    }
  };

  const handleJoinAsParticipant = async (participant: Participant) => {
    if (!session?.user?.id) {
      showToast("Vous devez être connecté pour rejoindre l'événement", {
        type: "error",
      });
      return;
    }

    try {
      setJoining(true);

      // Mettre à jour le participant avec l'ID utilisateur et le statut accepté
      const updatedParticipant = await updateParticipant(participant.id, {
        user_id: session.user.id,
        status: "accepted",
      });

      if (updatedParticipant) {
        showToast(
          `Vous avez rejoint l'événement en tant que ${participant.anonymous_name} !`,
          {
            type: "success",
          }
        );
        router.push(`/event/${id}`);
      } else {
        throw new Error("Échec de la mise à jour");
      }
    } catch (error) {
      console.error("Error joining event:", error);
      showToast("Erreur lors de la participation à l'événement", {
        type: "error",
      });
    } finally {
      setJoining(false);
    }
  };

  const handleJoinWithCustomName = async () => {
    if (!customName.trim()) {
      showToast("Veuillez saisir votre nom", { type: "error" });
      return;
    }

    if (!session?.user?.id) {
      showToast("Vous devez être connecté pour rejoindre l'événement", {
        type: "error",
      });
      return;
    }

    try {
      setJoining(true);

      // Créer un nouveau participant
      const newParticipant = await createParticipant({
        event_id: parseInt(id!),
        user_id: session.user.id,
        anonymous_name: customName.trim(),
        role: "guest",
        status: "accepted",
      });

      if (newParticipant) {
        showToast(
          `Vous avez rejoint l'événement en tant que ${customName.trim()} !`,
          {
            type: "success",
          }
        );
        router.push(`/event/${id}`);
      } else {
        throw new Error("Échec de la création du participant");
      }
    } catch (error) {
      console.error("Error creating participant:", error);
      showToast("Erreur lors de la participation à l'événement", {
        type: "error",
      });
    } finally {
      setJoining(false);
    }
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-muted-foreground">
          Chargement de l'événement...
        </Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <Text className="text-destructive">Événement non trouvé</Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-background">
      <View className={Platform.OS === "web" ? "max-w-md mx-auto p-6" : "p-4"}>
        {/* En-tête */}
        <View className="items-center mb-8 mt-8">
          <Text className="text-2xl font-bold text-center mb-2">
            Rejoindre l'événement
          </Text>
          <Text className="text-muted-foreground text-center">
            Choisissez votre identité dans la liste ci-dessous
          </Text>
        </View>

        {/* Détails de l'événement */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex-row items-center">
              <Text className="text-2xl mr-3">{event.icon || "🎉"}</Text>
              <Text className="text-lg font-semibold">{event.title}</Text>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-sm text-muted-foreground mb-2">
              📅{" "}
              {new Date(event.date_time).toLocaleDateString("fr-FR", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}{" "}
              à{" "}
              {new Date(event.date_time).toLocaleTimeString("fr-FR", {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </Text>
            {event.location && (
              <Text className="text-sm text-muted-foreground mb-2">
                📍 {event.location}
              </Text>
            )}
            {event.description && (
              <Text className="text-sm text-foreground">
                {event.description}
              </Text>
            )}
          </CardContent>
        </Card>

        {/* Liste des participants disponibles */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Qui êtes-vous ?</CardTitle>
          </CardHeader>
          <CardContent>
            {participants.length === 0 ? (
              <Text className="text-muted-foreground text-center py-4">
                Aucun participant disponible. L'organisateur n'a peut-être pas
                encore ajouté de participants.
              </Text>
            ) : (
              <View className="gap-3">
                {participants.map((participant) => (
                  <Button
                    key={participant.id}
                    variant="outline"
                    onPress={() => handleJoinAsParticipant(participant)}
                    disabled={joining}
                    className="h-16 justify-start px-4"
                  >
                    <View className="flex-row items-center justify-between w-full">
                      <Text className="text-lg font-medium">
                        {participant.anonymous_name}
                      </Text>
                      <Text className="text-muted-foreground">👤</Text>
                    </View>
                  </Button>
                ))}
              </View>
            )}

            {/* Option pour ajouter un nom personnalisé */}
            <View className="mt-4 pt-4 border-t border-border">
              <Text className="text-sm text-muted-foreground mb-3">
                Votre nom n'est pas dans la liste ?
              </Text>

              {!showCustomNameInput ? (
                <Button
                  variant="outline"
                  onPress={() => setShowCustomNameInput(true)}
                  disabled={joining}
                  className="h-12"
                >
                  <Text>➕ Ajouter mon nom</Text>
                </Button>
              ) : (
                <View className="gap-3">
                  <View>
                    <Label htmlFor="custom-name">Votre nom</Label>
                    <Input
                      id="custom-name"
                      placeholder="Saisissez votre nom"
                      value={customName}
                      onChangeText={(text) => {
                        console.log("Input changed:", text);
                        setCustomName(text);
                      }}
                      className="mt-2"
                      autoCapitalize="words"
                      autoCorrect={false}
                    />
                  </View>

                  <View className="flex-row gap-3">
                    <Button
                      onPress={handleJoinWithCustomName}
                      disabled={joining}
                      className="flex-1 h-12"
                    >
                      <Text className="text-primary-foreground">
                        {joining ? "Rejoindre..." : "🚀 Rejoindre"}
                      </Text>
                    </Button>

                    <Button
                      variant="outline"
                      onPress={() => {
                        setShowCustomNameInput(false);
                        setCustomName("");
                      }}
                      disabled={joining}
                      className="h-12 px-4"
                    >
                      <Text>❌</Text>
                    </Button>
                  </View>
                </View>
              )}
            </View>
          </CardContent>
        </Card>

        {/* Bouton retour */}
        <Button
          variant="ghost"
          onPress={() => router.push("/(tabs)")}
          className="h-12 mb-8"
        >
          <Text className="text-muted-foreground">Retour à l'accueil</Text>
        </Button>
      </View>
    </ScrollView>
  );
}
