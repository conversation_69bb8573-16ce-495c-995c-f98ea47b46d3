import React from "react";
import { View } from "react-native";
import { Text } from "./text";
import { cn } from "~/lib/utils";

interface BadgeProps {
  children: React.ReactNode;
  variant?: "default" | "secondary" | "destructive" | "outline";
  className?: string;
}

export function Badge({ 
  children, 
  variant = "default", 
  className 
}: BadgeProps) {
  const baseClasses = "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold";
  
  const variantClasses = {
    default: "bg-primary text-primary-foreground",
    secondary: "bg-secondary text-secondary-foreground",
    destructive: "bg-destructive text-destructive-foreground",
    outline: "border border-border text-foreground bg-background",
  };

  return (
    <View className={cn(
      baseClasses,
      variantClasses[variant],
      className
    )}>
      {children}
    </View>
  );
}
